using System;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows.Forms;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容替换设置窗体 - 事件处理
    /// </summary>
    public partial class ContentReplacementForm
    {
        #region 文本替换规则事件

        /// <summary>
        /// 添加文本替换规则
        /// </summary>
        private void BtnAddTextReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new TextReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.TextReplacement.ReplacementRules.Add(ruleForm.Rule);
                    LoadTextReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑文本替换规则
        /// </summary>
        private void BtnEditTextReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("TextReplacement", "listViewTextReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is TextReplacementRule rule)
                    {
                        using var ruleForm = new TextReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadTextReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除文本替换规则
        /// </summary>
        private void BtnDeleteTextReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("TextReplacement", "listViewTextReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is TextReplacementRule rule)
                    {
                        _currentSettings.TextReplacement.ReplacementRules.Remove(rule);
                        LoadTextReplacementRules();
                    }
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入文本替换规则（Excel文件）
        /// </summary>
        private void BtnImportTextReplacementRules_Click(object? sender, EventArgs e)
        {
            try
            {
                using var openFileDialog = new OpenFileDialog
                {
                    Title = "导入文本替换规则",
                    Filter = "Excel文件 (*.xlsx;*.xls)|*.xlsx;*.xls|所有文件 (*.*)|*.*",
                    DefaultExt = "xlsx"
                };

                if (openFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var excelService = new Services.ExcelImportExportService();
                    var importedRules = excelService.ImportTextReplacementRules(openFileDialog.FileName);

                    if (importedRules != null && importedRules.Count > 0)
                    {
                        var result = MessageBox.Show($"找到 {importedRules.Count} 条规则，是否导入？", "确认导入",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (result == DialogResult.Yes)
                        {
                            foreach (var rule in importedRules)
                            {
                                _currentSettings.TextReplacement.ReplacementRules.Add(rule);
                            }
                            LoadTextReplacementRules();
                            MessageBox.Show($"成功导入 {importedRules.Count} 条规则", "导入成功",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    else
                    {
                        MessageBox.Show("Excel文件中没有找到有效的规则", "导入失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入Excel文件失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导出文本替换规则
        /// </summary>
        private void BtnExportTextReplacementRules_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_currentSettings.TextReplacement.ReplacementRules.Count == 0)
                {
                    MessageBox.Show("没有可导出的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using var saveFileDialog = new SaveFileDialog
                {
                    Title = "导出文本替换规则",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx|JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = "xlsx",
                    FileName = $"文本替换规则_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var extension = Path.GetExtension(saveFileDialog.FileName).ToLower();

                    if (extension == ".xlsx" || extension == ".xls")
                    {
                        // 导出为Excel格式
                        var excelService = new Services.ExcelImportExportService();
                        excelService.ExportTextReplacementRules(_currentSettings.TextReplacement.ReplacementRules, saveFileDialog.FileName);
                    }
                    else
                    {
                        // 导出为JSON格式
                        var jsonContent = JsonSerializer.Serialize(_currentSettings.TextReplacement.ReplacementRules.ToArray(),
                            new JsonSerializerOptions { WriteIndented = true, Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping });
                        File.WriteAllText(saveFileDialog.FileName, jsonContent);
                    }

                    MessageBox.Show($"成功导出 {_currentSettings.TextReplacement.ReplacementRules.Count} 条规则", "导出成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出文本替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 下载Excel模板
        /// </summary>
        private void BtnDownloadExcelTemplate_Click(object? sender, EventArgs e)
        {
            try
            {
                using var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存Excel模板",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx|所有文件 (*.*)|*.*",
                    DefaultExt = "xlsx",
                    FileName = "文本替换规则模板.xlsx"
                };

                if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var excelService = new Services.ExcelImportExportService();
                    excelService.CreateTextReplacementTemplate(saveFileDialog.FileName);

                    MessageBox.Show("Excel模板下载成功！\n\n请在模板中填写替换规则，然后使用\"导入Excel\"功能导入规则。",
                        "下载成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"下载Excel模板失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 形状替换规则事件

        /// <summary>
        /// 添加图片替换规则
        /// </summary>
        private void BtnAddImageReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new ImageReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.ShapeReplacement.ImageReplacementRules.Add(ruleForm.Rule);
                    LoadImageReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加图片替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑图片替换规则
        /// </summary>
        private void BtnEditImageReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewImageReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is ImageReplacementRule rule)
                    {
                        using var ruleForm = new ImageReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadImageReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑图片替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除图片替换规则
        /// </summary>
        private void BtnDeleteImageReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewImageReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is ImageReplacementRule rule)
                    {
                        _currentSettings.ShapeReplacement.ImageReplacementRules.Remove(rule);
                        LoadImageReplacementRules();
                    }
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除图片替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 添加文本框替换规则
        /// </summary>
        private void BtnAddTextBoxReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new TextBoxReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.ShapeReplacement.TextBoxReplacementRules.Add(ruleForm.Rule);
                    LoadTextBoxReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加文本框替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑文本框替换规则
        /// </summary>
        private void BtnEditTextBoxReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewTextBoxReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is TextBoxReplacementRule rule)
                    {
                        using var ruleForm = new TextBoxReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadTextBoxReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑文本框替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除文本框替换规则
        /// </summary>
        private void BtnDeleteTextBoxReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewTextBoxReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is TextBoxReplacementRule rule)
                    {
                        _currentSettings.ShapeReplacement.TextBoxReplacementRules.Remove(rule);
                        LoadTextBoxReplacementRules();
                    }
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除文本框替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 添加形状样式替换规则
        /// </summary>
        private void BtnAddShapeStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new ShapeStyleReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.ShapeReplacement.ShapeStyleReplacementRules.Add(ruleForm.Rule);
                    LoadShapeStyleReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加形状样式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑形状样式替换规则
        /// </summary>
        private void BtnEditShapeStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewShapeStyleReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is ShapeStyleReplacementRule rule)
                    {
                        using var ruleForm = new ShapeStyleReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadShapeStyleReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑形状样式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除形状样式替换规则
        /// </summary>
        private void BtnDeleteShapeStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("ShapeReplacement", "listViewShapeStyleReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is ShapeStyleReplacementRule rule)
                    {
                        _currentSettings.ShapeReplacement.ShapeStyleReplacementRules.Remove(rule);
                        LoadShapeStyleReplacementRules();
                    }
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除形状样式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 字体替换规则事件

        /// <summary>
        /// 添加字体名称替换规则
        /// </summary>
        private void BtnAddFontNameReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new FontNameReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.FontReplacement.FontNameReplacementRules.Add(ruleForm.Rule);
                    LoadFontNameReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加字体名称替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑字体名称替换规则
        /// </summary>
        private void BtnEditFontNameReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("FontReplacement", "listViewFontNameReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is FontNameReplacementRule rule)
                    {
                        using var ruleForm = new FontNameReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadFontNameReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑字体名称替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除字体名称替换规则
        /// </summary>
        private void BtnDeleteFontNameReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("FontReplacement", "listViewFontNameReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is FontNameReplacementRule rule)
                    {
                        _currentSettings.FontReplacement.FontNameReplacementRules.Remove(rule);
                        LoadFontNameReplacementRules();
                    }
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除字体名称替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 添加字体样式替换规则
        /// </summary>
        private void BtnAddFontStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new FontStyleReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.FontReplacement.FontStyleReplacementRules.Add(ruleForm.Rule);
                    LoadFontStyleReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加字体样式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑字体样式替换规则
        /// </summary>
        private void BtnEditFontStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("FontReplacement", "listViewFontStyleReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is FontStyleReplacementRule rule)
                    {
                        using var ruleForm = new FontStyleReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadFontStyleReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑字体样式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除字体样式替换规则
        /// </summary>
        private void BtnDeleteFontStyleReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("FontReplacement", "listViewFontStyleReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is FontStyleReplacementRule rule)
                    {
                        _currentSettings.FontReplacement.FontStyleReplacementRules.Remove(rule);
                        LoadFontStyleReplacementRules();
                    }
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除字体样式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 添加字体到嵌入列表
        /// </summary>
        private void BtnAddFontToEmbed_Click(object? sender, EventArgs e)
        {
            try
            {
                // 使用简单的输入对话框
                string fontName = ShowInputDialog("请输入要嵌入的字体名称:", "添加字体");
                if (!string.IsNullOrWhiteSpace(fontName))
                {
                    var listBox = FindControlInPanel<ListBox>("FontReplacement", "listBoxFontsToEmbed");
                    if (listBox != null && !listBox.Items.Contains(fontName))
                    {
                        listBox.Items.Add(fontName);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加字体失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示输入对话框
        /// </summary>
        private string ShowInputDialog(string prompt, string title)
        {
            Form inputForm = new Form()
            {
                Width = 400,
                Height = 150,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = title,
                StartPosition = FormStartPosition.CenterParent,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label textLabel = new Label() { Left = 20, Top = 20, Width = 350, Text = prompt };
            TextBox textBox = new TextBox() { Left = 20, Top = 45, Width = 350 };
            Button confirmation = new Button() { Text = "确定", Left = 220, Width = 75, Top = 75, DialogResult = DialogResult.OK };
            Button cancel = new Button() { Text = "取消", Left = 300, Width = 75, Top = 75, DialogResult = DialogResult.Cancel };

            confirmation.Click += (sender, e) => { inputForm.Close(); };
            cancel.Click += (sender, e) => { inputForm.Close(); };

            inputForm.Controls.Add(textLabel);
            inputForm.Controls.Add(textBox);
            inputForm.Controls.Add(confirmation);
            inputForm.Controls.Add(cancel);
            inputForm.AcceptButton = confirmation;
            inputForm.CancelButton = cancel;

            return inputForm.ShowDialog(this) == DialogResult.OK ? textBox.Text : "";
        }

        /// <summary>
        /// 从嵌入列表移除字体
        /// </summary>
        private void BtnRemoveFontToEmbed_Click(object? sender, EventArgs e)
        {
            try
            {
                var listBox = FindControlInPanel<ListBox>("FontReplacement", "listBoxFontsToEmbed");
                if (listBox?.SelectedItem != null)
                {
                    listBox.Items.Remove(listBox.SelectedItem);
                }
                else
                {
                    MessageBox.Show("请选择要移除的字体", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"移除字体失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空字体嵌入列表
        /// </summary>
        private void BtnClearFontsToEmbed_Click(object? sender, EventArgs e)
        {
            try
            {
                var listBox = FindControlInPanel<ListBox>("FontReplacement", "listBoxFontsToEmbed");
                if (listBox != null)
                {
                    if (listBox.Items.Count > 0)
                    {
                        var result = MessageBox.Show("确定要清空所有字体吗？", "确认清空",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                        if (result == DialogResult.Yes)
                        {
                            listBox.Items.Clear();
                        }
                    }
                    else
                    {
                        MessageBox.Show("字体列表已经为空", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空字体列表失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 颜色替换规则事件

        /// <summary>
        /// 添加主题颜色替换规则
        /// </summary>
        private void BtnAddThemeColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("主题颜色替换功能需要深度集成Aspose.Slides的主题系统，\n当前版本暂不支持，建议使用自定义颜色替换功能。",
                "功能说明", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 编辑主题颜色替换规则
        /// </summary>
        private void BtnEditThemeColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("主题颜色替换功能需要深度集成Aspose.Slides的主题系统，\n当前版本暂不支持，建议使用自定义颜色替换功能。",
                "功能说明", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 删除主题颜色替换规则
        /// </summary>
        private void BtnDeleteThemeColorReplacementRule_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("主题颜色替换功能需要深度集成Aspose.Slides的主题系统，\n当前版本暂不支持，建议使用自定义颜色替换功能。",
                "功能说明", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 添加自定义颜色替换规则
        /// </summary>
        private void BtnAddCustomColorReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new CustomColorReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.ColorReplacement.CustomColorReplacementRules.Add(ruleForm.Rule);
                    LoadCustomColorReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加自定义颜色替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑自定义颜色替换规则
        /// </summary>
        private void BtnEditCustomColorReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("ColorReplacement", "listViewCustomColorReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is CustomColorReplacementRule rule)
                    {
                        using var ruleForm = new CustomColorReplacementRuleForm(rule);
                        if (ruleForm.ShowDialog(this) == DialogResult.OK)
                        {
                            LoadCustomColorReplacementRules();
                        }
                    }
                }
                else
                {
                    MessageBox.Show("请选择要编辑的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑自定义颜色替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除自定义颜色替换规则
        /// </summary>
        private void BtnDeleteCustomColorReplacementRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var listView = FindControlInPanel<ListView>("ColorReplacement", "listViewCustomColorReplacementRules");
                if (listView?.SelectedItems.Count > 0)
                {
                    var selectedItem = listView.SelectedItems[0];
                    if (selectedItem.Tag is CustomColorReplacementRule rule)
                    {
                        _currentSettings.ColorReplacement.CustomColorReplacementRules.Remove(rule);
                        LoadCustomColorReplacementRules();
                    }
                }
                else
                {
                    MessageBox.Show("请选择要删除的规则", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除自定义颜色替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 颜色选择和规则编辑事件

        /// <summary>
        /// 选择源颜色
        /// </summary>
        private void BtnSourceColor_Click(object? sender, EventArgs e)
        {
            try
            {
                var button = sender as Button;
                if (button == null) return;

                using var colorDialog = new ColorDialog();

                // 设置当前颜色
                if (button.BackColor != Color.Empty)
                    colorDialog.Color = button.BackColor;

                if (colorDialog.ShowDialog(this) == DialogResult.OK)
                {
                    button.BackColor = colorDialog.Color;
                    button.Text = $"RGB({colorDialog.Color.R},{colorDialog.Color.G},{colorDialog.Color.B})";

                    // 根据颜色亮度调整文字颜色
                    var brightness = (colorDialog.Color.R * 299 + colorDialog.Color.G * 587 + colorDialog.Color.B * 114) / 1000;
                    button.ForeColor = brightness > 128 ? Color.Black : Color.White;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择源颜色失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 选择目标颜色
        /// </summary>
        private void BtnTargetColor_Click(object? sender, EventArgs e)
        {
            try
            {
                var button = sender as Button;
                if (button == null) return;

                using var colorDialog = new ColorDialog();

                // 设置当前颜色
                if (button.BackColor != Color.Empty)
                    colorDialog.Color = button.BackColor;

                if (colorDialog.ShowDialog(this) == DialogResult.OK)
                {
                    button.BackColor = colorDialog.Color;
                    button.Text = $"RGB({colorDialog.Color.R},{colorDialog.Color.G},{colorDialog.Color.B})";

                    // 根据颜色亮度调整文字颜色
                    var brightness = (colorDialog.Color.R * 299 + colorDialog.Color.G * 587 + colorDialog.Color.B * 114) / 1000;
                    button.ForeColor = brightness > 128 ? Color.Black : Color.White;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择目标颜色失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        /// <summary>
        /// 保存颜色规则
        /// </summary>
        private void BtnSaveColorRule_Click(object? sender, EventArgs e)
        {
            try
            {
                var panel = _tabPanels["ColorReplacement"];

                // 获取编辑控件
                var txtRuleName = FindControlInPanel<TextBox>("ColorReplacement", "txtColorRuleName");
                var btnSourceColor = FindControlInPanel<Button>("ColorReplacement", "btnSourceColor");
                var btnTargetColor = FindControlInPanel<Button>("ColorReplacement", "btnTargetColor");
                var chkApplyToTextColor = FindControlInPanel<CheckBox>("ColorReplacement", "chkApplyToTextColor");
                var chkApplyToFillColor = FindControlInPanel<CheckBox>("ColorReplacement", "chkApplyToFillColor");
                var chkApplyToBorderColor = FindControlInPanel<CheckBox>("ColorReplacement", "chkApplyToBorderColor");
                var chkApplyToBackgroundColor = FindControlInPanel<CheckBox>("ColorReplacement", "chkApplyToBackgroundColor");
                var chkIsEnabled = FindControlInPanel<CheckBox>("ColorReplacement", "chkColorRuleEnabled");

                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName?.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName?.Focus();
                    return;
                }

                if (!(chkApplyToTextColor?.Checked == true || chkApplyToFillColor?.Checked == true ||
                      chkApplyToBorderColor?.Checked == true || chkApplyToBackgroundColor?.Checked == true))
                {
                    MessageBox.Show("请至少选择一种应用范围", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 创建规则
                var rule = new CustomColorReplacementRule
                {
                    RuleName = txtRuleName.Text.Trim(),
                    SourceColor = ColorToHexString(btnSourceColor?.BackColor ?? Color.Black),
                    TargetColor = ColorToHexString(btnTargetColor?.BackColor ?? Color.White),
                    ApplyToTextColor = chkApplyToTextColor?.Checked ?? false,
                    ApplyToFillColor = chkApplyToFillColor?.Checked ?? false,
                    ApplyToBorderColor = chkApplyToBorderColor?.Checked ?? false,
                    ApplyToBackgroundColor = chkApplyToBackgroundColor?.Checked ?? false,
                    IsEnabled = chkIsEnabled?.Checked ?? true,
                    CreatedTime = DateTime.Now
                };

                // 添加到规则列表
                _currentSettings.ColorReplacement.CustomColorReplacementRules.Add(rule);

                // 刷新规则列表
                LoadCustomColorReplacementRules();

                // 清空编辑器
                ClearColorRuleEditor();

                MessageBox.Show("颜色替换规则保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存颜色规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空颜色规则编辑器
        /// </summary>
        private void BtnClearColorRule_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearColorRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空颜色规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空颜色规则编辑器
        /// </summary>
        private void ClearColorRuleEditor()
        {
            try
            {
                var txtRuleName = FindControlInPanel<TextBox>("ColorReplacement", "txtColorRuleName");
                var btnSourceColor = FindControlInPanel<Button>("ColorReplacement", "btnSourceColor");
                var btnTargetColor = FindControlInPanel<Button>("ColorReplacement", "btnTargetColor");
                var chkApplyToTextColor = FindControlInPanel<CheckBox>("ColorReplacement", "chkApplyToTextColor");
                var chkApplyToFillColor = FindControlInPanel<CheckBox>("ColorReplacement", "chkApplyToFillColor");
                var chkApplyToBorderColor = FindControlInPanel<CheckBox>("ColorReplacement", "chkApplyToBorderColor");
                var chkApplyToBackgroundColor = FindControlInPanel<CheckBox>("ColorReplacement", "chkApplyToBackgroundColor");
                var chkIsEnabled = FindControlInPanel<CheckBox>("ColorReplacement", "chkColorRuleEnabled");

                // 清空所有控件
                if (txtRuleName != null) txtRuleName.Clear();

                if (btnSourceColor != null)
                {
                    btnSourceColor.BackColor = Color.Black;
                    btnSourceColor.ForeColor = Color.White;
                    btnSourceColor.Text = "选择源颜色";
                }

                if (btnTargetColor != null)
                {
                    btnTargetColor.BackColor = Color.White;
                    btnTargetColor.ForeColor = Color.Black;
                    btnTargetColor.Text = "选择目标颜色";
                }

                if (chkApplyToTextColor != null) chkApplyToTextColor.Checked = true;
                if (chkApplyToFillColor != null) chkApplyToFillColor.Checked = true;
                if (chkApplyToBorderColor != null) chkApplyToBorderColor.Checked = false;
                if (chkApplyToBackgroundColor != null) chkApplyToBackgroundColor.Checked = false;
                if (chkIsEnabled != null) chkIsEnabled.Checked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空颜色规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 规则编辑按钮事件

        #region 文本替换规则编辑事件

        /// <summary>
        /// 新建文本规则
        /// </summary>
        private void BtnNewTextRule_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearTextRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建文本规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 保存文本规则
        /// </summary>
        private void BtnSaveTextRule_Click(object? sender, EventArgs e)
        {
            try
            {
                // 获取编辑控件
                var txtRuleName = FindControlInPanel<TextBox>("TextReplacement", "txtTextRuleName");
                var txtFindText = FindControlInPanel<TextBox>("TextReplacement", "txtFindText");
                var txtReplaceText = FindControlInPanel<TextBox>("TextReplacement", "txtReplaceText");
                var chkUseRegex = FindControlInPanel<CheckBox>("TextReplacement", "chkUseRegex");
                var chkCaseSensitive = FindControlInPanel<CheckBox>("TextReplacement", "chkCaseSensitive");
                var chkWholeWord = FindControlInPanel<CheckBox>("TextReplacement", "chkWholeWord");
                var cmbRangeType = FindControlInPanel<ComboBox>("TextReplacement", "cmbRangeType");
                var chkIsEnabled = FindControlInPanel<CheckBox>("TextReplacement", "chkTextRuleEnabled");

                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName?.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtFindText?.Text))
                {
                    MessageBox.Show("请输入查找文本", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtFindText?.Focus();
                    return;
                }

                // 创建规则
                var rule = new TextReplacementRule
                {
                    RuleName = txtRuleName.Text.Trim(),
                    FindText = txtFindText.Text,
                    ReplaceText = txtReplaceText?.Text ?? "",
                    UseRegex = chkUseRegex?.Checked ?? false,
                    CaseSensitive = chkCaseSensitive?.Checked ?? false,
                    WholeWord = chkWholeWord?.Checked ?? false,
                    RangeType = (TextReplacementRangeType)(cmbRangeType?.SelectedIndex ?? 0),
                    IsEnabled = chkIsEnabled?.Checked ?? true,
                    CreatedTime = DateTime.Now
                };

                // 添加到规则列表
                _currentSettings.TextReplacement.ReplacementRules.Add(rule);

                // 刷新规则列表
                LoadTextReplacementRules();

                // 清空编辑器
                ClearTextRuleEditor();

                MessageBox.Show("文本替换规则保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文本规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空文本规则编辑器
        /// </summary>
        private void BtnClearTextRule_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearTextRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空文本规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空文本规则编辑器
        /// </summary>
        private void ClearTextRuleEditor()
        {
            try
            {
                var txtRuleName = FindControlInPanel<TextBox>("TextReplacement", "txtTextRuleName");
                var txtFindText = FindControlInPanel<TextBox>("TextReplacement", "txtFindText");
                var txtReplaceText = FindControlInPanel<TextBox>("TextReplacement", "txtReplaceText");
                var chkUseRegex = FindControlInPanel<CheckBox>("TextReplacement", "chkUseRegex");
                var chkCaseSensitive = FindControlInPanel<CheckBox>("TextReplacement", "chkCaseSensitive");
                var chkWholeWord = FindControlInPanel<CheckBox>("TextReplacement", "chkWholeWord");
                var cmbRangeType = FindControlInPanel<ComboBox>("TextReplacement", "cmbRangeType");
                var chkIsEnabled = FindControlInPanel<CheckBox>("TextReplacement", "chkTextRuleEnabled");

                // 清空所有控件
                txtRuleName?.Clear();
                txtFindText?.Clear();
                txtReplaceText?.Clear();
                if (chkUseRegex != null) chkUseRegex.Checked = false;
                if (chkCaseSensitive != null) chkCaseSensitive.Checked = false;
                if (chkWholeWord != null) chkWholeWord.Checked = false;
                if (cmbRangeType != null) cmbRangeType.SelectedIndex = 0;
                if (chkIsEnabled != null) chkIsEnabled.Checked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空文本规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 形状替换规则编辑事件

        #region 图片替换规则编辑事件



        /// <summary>
        /// 保存图片规则
        /// </summary>
        private void BtnSaveImageRule_Click(object? sender, EventArgs e)
        {
            try
            {
                // 获取编辑控件
                var txtRuleName = FindControlInPanel<TextBox>("ShapeReplacement", "txtImageRuleName");
                var txtSourceImage = FindControlInPanel<TextBox>("ShapeReplacement", "txtSourceImagePath");
                var txtTargetImage = FindControlInPanel<TextBox>("ShapeReplacement", "txtTargetImagePath");
                var cmbMatchType = FindControlInPanel<ComboBox>("ShapeReplacement", "cmbImageMatchType");
                var chkIsEnabled = FindControlInPanel<CheckBox>("ShapeReplacement", "chkImageRuleEnabled");

                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName?.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtSourceImage?.Text))
                {
                    MessageBox.Show("请选择源图片", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtSourceImage?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtTargetImage?.Text))
                {
                    MessageBox.Show("请选择目标图片", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtTargetImage?.Focus();
                    return;
                }

                // 创建规则
                var rule = new ImageReplacementRule
                {
                    RuleName = txtRuleName.Text.Trim(),
                    SourceImagePath = txtSourceImage.Text.Trim(),
                    TargetImagePath = txtTargetImage.Text.Trim(),
                    MatchByFileName = cmbMatchType?.SelectedIndex == 0,
                    MatchByContent = cmbMatchType?.SelectedIndex == 1,
                    MatchByPosition = cmbMatchType?.SelectedIndex == 2,
                    MatchBySize = cmbMatchType?.SelectedIndex == 3,
                    IsEnabled = chkIsEnabled?.Checked ?? true,
                    CreatedTime = DateTime.Now
                };

                // 添加到规则列表
                _currentSettings.ShapeReplacement.ImageReplacementRules.Add(rule);

                // 刷新规则列表
                LoadImageReplacementRules();

                // 清空编辑器
                ClearImageRuleEditor();

                MessageBox.Show("图片替换规则保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图片规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空图片规则编辑器
        /// </summary>
        private void BtnClearImageRule_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearImageRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空图片规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空图片规则编辑器
        /// </summary>
        private void ClearImageRuleEditor()
        {
            try
            {
                var txtRuleName = FindControlInPanel<TextBox>("ShapeReplacement", "txtImageRuleName");
                var txtSourceImage = FindControlInPanel<TextBox>("ShapeReplacement", "txtSourceImagePath");
                var txtTargetImage = FindControlInPanel<TextBox>("ShapeReplacement", "txtTargetImagePath");
                var cmbMatchType = FindControlInPanel<ComboBox>("ShapeReplacement", "cmbImageMatchType");
                var chkIsEnabled = FindControlInPanel<CheckBox>("ShapeReplacement", "chkImageRuleEnabled");

                // 清空所有控件
                txtRuleName?.Clear();
                txtSourceImage?.Clear();
                txtTargetImage?.Clear();
                if (cmbMatchType != null) cmbMatchType.SelectedIndex = 0;
                if (chkIsEnabled != null) chkIsEnabled.Checked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空图片规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 文本框替换规则编辑事件



        /// <summary>
        /// 保存文本框规则
        /// </summary>
        private void BtnSaveTextBoxRule_Click(object? sender, EventArgs e)
        {
            try
            {
                // 获取编辑控件
                var txtRuleName = FindControlInPanel<TextBox>("ShapeReplacement", "txtTextBoxRuleName");
                var txtSourceText = FindControlInPanel<TextBox>("ShapeReplacement", "txtSourceTextContent");
                var txtTargetText = FindControlInPanel<TextBox>("ShapeReplacement", "txtTargetTextContent");
                var chkMatchByContent = FindControlInPanel<CheckBox>("ShapeReplacement", "chkMatchByContent");
                var chkMatchByPosition = FindControlInPanel<CheckBox>("ShapeReplacement", "chkMatchByPosition");
                var numMatchX = FindControlInPanel<NumericUpDown>("ShapeReplacement", "numMatchX");
                var numMatchY = FindControlInPanel<NumericUpDown>("ShapeReplacement", "numMatchY");
                var chkIsEnabled = FindControlInPanel<CheckBox>("ShapeReplacement", "chkTextBoxRuleEnabled");

                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName?.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtSourceText?.Text))
                {
                    MessageBox.Show("请输入源文本内容", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtSourceText?.Focus();
                    return;
                }

                // 创建规则
                var rule = new TextBoxReplacementRule
                {
                    RuleName = txtRuleName.Text.Trim(),
                    SourceTextContent = txtSourceText.Text,
                    TargetTextContent = txtTargetText?.Text ?? "",
                    MatchByContent = chkMatchByContent?.Checked ?? true,
                    MatchByPosition = chkMatchByPosition?.Checked ?? false,
                    MatchX = (int)(numMatchX?.Value ?? 0),
                    MatchY = (int)(numMatchY?.Value ?? 0),
                    IsEnabled = chkIsEnabled?.Checked ?? true,
                    CreatedTime = DateTime.Now
                };

                // 添加到规则列表
                _currentSettings.ShapeReplacement.TextBoxReplacementRules.Add(rule);

                // 刷新规则列表
                LoadTextBoxReplacementRules();

                // 清空编辑器
                ClearTextBoxRuleEditor();

                MessageBox.Show("文本框替换规则保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文本框规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空文本框规则编辑器
        /// </summary>
        private void BtnClearTextBoxRule_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearTextBoxRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空文本框规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空文本框规则编辑器
        /// </summary>
        private void ClearTextBoxRuleEditor()
        {
            try
            {
                var txtRuleName = FindControlInPanel<TextBox>("ShapeReplacement", "txtTextBoxRuleName");
                var txtSourceText = FindControlInPanel<TextBox>("ShapeReplacement", "txtSourceTextContent");
                var txtTargetText = FindControlInPanel<TextBox>("ShapeReplacement", "txtTargetTextContent");
                var chkMatchByContent = FindControlInPanel<CheckBox>("ShapeReplacement", "chkMatchByContent");
                var chkMatchByPosition = FindControlInPanel<CheckBox>("ShapeReplacement", "chkMatchByPosition");
                var numMatchX = FindControlInPanel<NumericUpDown>("ShapeReplacement", "numMatchX");
                var numMatchY = FindControlInPanel<NumericUpDown>("ShapeReplacement", "numMatchY");
                var chkIsEnabled = FindControlInPanel<CheckBox>("ShapeReplacement", "chkTextBoxRuleEnabled");

                // 清空所有控件
                txtRuleName?.Clear();
                txtSourceText?.Clear();
                txtTargetText?.Clear();
                if (chkMatchByContent != null) chkMatchByContent.Checked = true;
                if (chkMatchByPosition != null) chkMatchByPosition.Checked = false;
                if (numMatchX != null) numMatchX.Value = 0;
                if (numMatchY != null) numMatchY.Value = 0;
                if (chkIsEnabled != null) chkIsEnabled.Checked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空文本框规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 形状样式替换规则编辑事件



        /// <summary>
        /// 保存形状样式规则
        /// </summary>
        private void BtnSaveShapeStyleRule_Click(object? sender, EventArgs e)
        {
            try
            {
                // 获取编辑控件
                var txtRuleName = FindControlInPanel<TextBox>("ShapeReplacement", "txtShapeStyleRuleName");
                var cmbSourceShapeType = FindControlInPanel<ComboBox>("ShapeReplacement", "cmbSourceShapeType");
                var cmbTargetShapeType = FindControlInPanel<ComboBox>("ShapeReplacement", "cmbTargetShapeType");
                var chkReplaceFillColor = FindControlInPanel<CheckBox>("ShapeReplacement", "chkReplaceFillColor");
                var chkReplaceBorderColor = FindControlInPanel<CheckBox>("ShapeReplacement", "chkReplaceBorderColor");
                var chkIsEnabled = FindControlInPanel<CheckBox>("ShapeReplacement", "chkShapeStyleRuleEnabled");

                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName?.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName?.Focus();
                    return;
                }

                if (cmbSourceShapeType?.SelectedIndex < 0)
                {
                    MessageBox.Show("请选择源形状类型", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbSourceShapeType?.Focus();
                    return;
                }

                if (cmbTargetShapeType?.SelectedIndex < 0)
                {
                    MessageBox.Show("请选择目标形状类型", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbTargetShapeType?.Focus();
                    return;
                }

                // 创建规则
                var rule = new ShapeStyleReplacementRule
                {
                    RuleName = txtRuleName.Text.Trim(),
                    SourceShapeType = cmbSourceShapeType?.Text ?? "",
                    TargetShapeType = cmbTargetShapeType?.Text ?? "",
                    ReplaceFillColor = chkReplaceFillColor?.Checked ?? false,
                    ReplaceBorderColor = chkReplaceBorderColor?.Checked ?? false,
                    IsEnabled = chkIsEnabled?.Checked ?? true,
                    CreatedTime = DateTime.Now
                };

                // 添加到规则列表
                _currentSettings.ShapeReplacement.ShapeStyleReplacementRules.Add(rule);

                // 刷新规则列表
                LoadShapeStyleReplacementRules();

                // 清空编辑器
                ClearShapeStyleRuleEditor();

                MessageBox.Show("形状样式替换规则保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存形状样式规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空形状样式规则编辑器
        /// </summary>
        private void BtnClearShapeStyleRule_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearShapeStyleRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空形状样式规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空形状样式规则编辑器
        /// </summary>
        private void ClearShapeStyleRuleEditor()
        {
            try
            {
                var txtRuleName = FindControlInPanel<TextBox>("ShapeReplacement", "txtShapeStyleRuleName");
                var cmbSourceShapeType = FindControlInPanel<ComboBox>("ShapeReplacement", "cmbSourceShapeType");
                var cmbTargetShapeType = FindControlInPanel<ComboBox>("ShapeReplacement", "cmbTargetShapeType");
                var chkReplaceFillColor = FindControlInPanel<CheckBox>("ShapeReplacement", "chkReplaceFillColor");
                var chkReplaceBorderColor = FindControlInPanel<CheckBox>("ShapeReplacement", "chkReplaceBorderColor");
                var chkIsEnabled = FindControlInPanel<CheckBox>("ShapeReplacement", "chkShapeStyleRuleEnabled");

                // 清空所有控件
                txtRuleName?.Clear();
                if (cmbSourceShapeType != null) cmbSourceShapeType.SelectedIndex = -1;
                if (cmbTargetShapeType != null) cmbTargetShapeType.SelectedIndex = -1;
                if (chkReplaceFillColor != null) chkReplaceFillColor.Checked = false;
                if (chkReplaceBorderColor != null) chkReplaceBorderColor.Checked = false;
                if (chkIsEnabled != null) chkIsEnabled.Checked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空形状样式规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 字体替换规则编辑事件

        #region 字体名称替换规则编辑事件



        /// <summary>
        /// 保存字体名称规则
        /// </summary>
        private void BtnSaveFontNameRule_Click(object? sender, EventArgs e)
        {
            try
            {
                // 获取编辑控件
                var txtRuleName = FindControlInPanel<TextBox>("FontReplacement", "txtFontNameRuleName");
                var cmbSourceFont = FindControlInPanel<ComboBox>("FontReplacement", "cmbSourceFont");
                var cmbTargetFont = FindControlInPanel<ComboBox>("FontReplacement", "cmbTargetFont");
                var chkExactMatch = FindControlInPanel<CheckBox>("FontReplacement", "chkExactMatch");
                var chkIncludeSubFonts = FindControlInPanel<CheckBox>("FontReplacement", "chkIncludeSubFonts");
                var chkIsEnabled = FindControlInPanel<CheckBox>("FontReplacement", "chkFontNameRuleEnabled");

                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName?.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(cmbSourceFont?.Text))
                {
                    MessageBox.Show("请输入或选择源字体名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbSourceFont?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(cmbTargetFont?.Text))
                {
                    MessageBox.Show("请输入或选择目标字体名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbTargetFont?.Focus();
                    return;
                }

                // 创建规则
                var rule = new FontNameReplacementRule
                {
                    RuleName = txtRuleName.Text.Trim(),
                    SourceFontName = cmbSourceFont.Text.Trim(),
                    TargetFontName = cmbTargetFont.Text.Trim(),
                    ExactMatch = chkExactMatch?.Checked ?? true,
                    IncludeSubFonts = chkIncludeSubFonts?.Checked ?? false,
                    IsEnabled = chkIsEnabled?.Checked ?? true,
                    CreatedTime = DateTime.Now
                };

                // 添加到规则列表
                _currentSettings.FontReplacement.FontNameReplacementRules.Add(rule);

                // 刷新规则列表
                LoadFontNameReplacementRules();

                // 清空编辑器
                ClearFontNameRuleEditor();

                MessageBox.Show("字体名称替换规则保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存字体名称规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空字体名称规则编辑器
        /// </summary>
        private void BtnClearFontNameRule_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearFontNameRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空字体名称规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空字体名称规则编辑器
        /// </summary>
        private void ClearFontNameRuleEditor()
        {
            try
            {
                var txtRuleName = FindControlInPanel<TextBox>("FontReplacement", "txtFontNameRuleName");
                var cmbSourceFont = FindControlInPanel<ComboBox>("FontReplacement", "cmbSourceFont");
                var cmbTargetFont = FindControlInPanel<ComboBox>("FontReplacement", "cmbTargetFont");
                var chkExactMatch = FindControlInPanel<CheckBox>("FontReplacement", "chkExactMatch");
                var chkIncludeSubFonts = FindControlInPanel<CheckBox>("FontReplacement", "chkIncludeSubFonts");
                var chkIsEnabled = FindControlInPanel<CheckBox>("FontReplacement", "chkFontNameRuleEnabled");

                // 清空所有控件
                txtRuleName?.Clear();
                if (cmbSourceFont != null) cmbSourceFont.Text = "";
                if (cmbTargetFont != null) cmbTargetFont.Text = "";
                if (chkExactMatch != null) chkExactMatch.Checked = true;
                if (chkIncludeSubFonts != null) chkIncludeSubFonts.Checked = false;
                if (chkIsEnabled != null) chkIsEnabled.Checked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空字体名称规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 字体样式替换规则编辑事件



        /// <summary>
        /// 保存字体样式规则
        /// </summary>
        private void BtnSaveFontStyleRule_Click(object? sender, EventArgs e)
        {
            try
            {
                // 获取编辑控件
                var txtRuleName = FindControlInPanel<TextBox>("FontReplacement", "txtFontStyleRuleName");
                var cmbSourceFont = FindControlInPanel<ComboBox>("FontReplacement", "cmbSourceFontStyle");
                var cmbTargetFont = FindControlInPanel<ComboBox>("FontReplacement", "cmbTargetFontStyle");
                var txtReplaceContent = FindControlInPanel<TextBox>("FontReplacement", "txtReplaceContent");
                var chkIsEnabled = FindControlInPanel<CheckBox>("FontReplacement", "chkFontStyleRuleEnabled");

                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName?.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(cmbSourceFont?.Text))
                {
                    MessageBox.Show("请输入或选择源字体样式", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbSourceFont?.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(cmbTargetFont?.Text))
                {
                    MessageBox.Show("请输入或选择目标字体样式", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbTargetFont?.Focus();
                    return;
                }

                // 创建规则
                var rule = new FontStyleReplacementRule
                {
                    RuleName = txtRuleName.Text.Trim(),
                    SourceFontStyle = cmbSourceFont.Text.Trim(),
                    TargetFontStyle = cmbTargetFont.Text.Trim(),
                    ReplaceContent = txtReplaceContent?.Text ?? "字体名称、字体大小、字体颜色、粗体、斜体、下划线",
                    IsEnabled = chkIsEnabled?.Checked ?? true,
                    CreatedTime = DateTime.Now
                };

                // 添加到规则列表
                _currentSettings.FontReplacement.FontStyleReplacementRules.Add(rule);

                // 刷新规则列表
                LoadFontStyleReplacementRules();

                // 清空编辑器
                ClearFontStyleRuleEditor();

                MessageBox.Show("字体样式替换规则保存成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存字体样式规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空字体样式规则编辑器
        /// </summary>
        private void BtnClearFontStyleRule_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearFontStyleRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空字体样式规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空字体样式规则编辑器
        /// </summary>
        private void ClearFontStyleRuleEditor()
        {
            try
            {
                var txtRuleName = FindControlInPanel<TextBox>("FontReplacement", "txtFontStyleRuleName");
                var cmbSourceFont = FindControlInPanel<ComboBox>("FontReplacement", "cmbSourceFontStyle");
                var cmbTargetFont = FindControlInPanel<ComboBox>("FontReplacement", "cmbTargetFontStyle");
                var txtReplaceContent = FindControlInPanel<TextBox>("FontReplacement", "txtReplaceContent");
                var chkIsEnabled = FindControlInPanel<CheckBox>("FontReplacement", "chkFontStyleRuleEnabled");

                // 清空所有控件
                txtRuleName?.Clear();
                if (cmbSourceFont != null) cmbSourceFont.Text = "";
                if (cmbTargetFont != null) cmbTargetFont.Text = "";
                if (txtReplaceContent != null) txtReplaceContent.Text = "字体名称、字体大小、字体颜色、粗体、斜体、下划线";
                if (chkIsEnabled != null) chkIsEnabled.Checked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空字体样式规则编辑器失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #endregion

        #endregion

        #endregion

        #region 缺失的事件处理方法

        /// <summary>
        /// 新建图片替换规则
        /// </summary>
        private void BtnNewImageRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new ImageReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.ShapeReplacement.ImageReplacementRules.Add(ruleForm.Rule);
                    LoadImageReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建图片替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑图片替换规则
        /// </summary>
        private void BtnEditImageRule_Click(object? sender, EventArgs e)
        {
            BtnEditImageReplacementRule_Click(sender, e);
        }

        /// <summary>
        /// 新建文本框替换规则
        /// </summary>
        private void BtnNewTextBoxRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new TextBoxReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.ShapeReplacement.TextBoxReplacementRules.Add(ruleForm.Rule);
                    LoadTextBoxReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建文本框替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑文本框替换规则
        /// </summary>
        private void BtnEditTextBoxRule_Click(object? sender, EventArgs e)
        {
            BtnEditTextBoxReplacementRule_Click(sender, e);
        }

        /// <summary>
        /// 新建形状样式替换规则
        /// </summary>
        private void BtnNewShapeStyleRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new ShapeStyleReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.ShapeReplacement.ShapeStyleReplacementRules.Add(ruleForm.Rule);
                    LoadShapeStyleReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建形状样式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑形状样式替换规则
        /// </summary>
        private void BtnEditShapeStyleRule_Click(object? sender, EventArgs e)
        {
            BtnEditShapeStyleReplacementRule_Click(sender, e);
        }

        /// <summary>
        /// 新建字体名称替换规则
        /// </summary>
        private void BtnNewFontNameRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new FontNameReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.FontReplacement.FontNameReplacementRules.Add(ruleForm.Rule);
                    LoadFontNameReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建字体名称替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑字体名称替换规则
        /// </summary>
        private void BtnEditFontNameRule_Click(object? sender, EventArgs e)
        {
            BtnEditFontNameReplacementRule_Click(sender, e);
        }

        /// <summary>
        /// 新建字体样式替换规则
        /// </summary>
        private void BtnNewFontStyleRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new FontStyleReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.FontReplacement.FontStyleReplacementRules.Add(ruleForm.Rule);
                    LoadFontStyleReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建字体样式替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑字体样式替换规则
        /// </summary>
        private void BtnEditFontStyleRule_Click(object? sender, EventArgs e)
        {
            BtnEditFontStyleReplacementRule_Click(sender, e);
        }

        /// <summary>
        /// 新建颜色替换规则
        /// </summary>
        private void BtnNewColorRule_Click(object? sender, EventArgs e)
        {
            try
            {
                using var ruleForm = new CustomColorReplacementRuleForm();
                if (ruleForm.ShowDialog(this) == DialogResult.OK)
                {
                    _currentSettings.ColorReplacement.CustomColorReplacementRules.Add(ruleForm.Rule);
                    LoadCustomColorReplacementRules();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建颜色替换规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 编辑颜色替换规则
        /// </summary>
        private void BtnEditColorRule_Click(object? sender, EventArgs e)
        {
            BtnEditCustomColorReplacementRule_Click(sender, e);
        }

        /// <summary>
        /// 规则编辑器新建按钮事件
        /// </summary>
        private void BtnNewImageRuleEdit_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearImageRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建图片规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnNewTextBoxRuleEdit_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearTextBoxRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建文本框规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnNewShapeStyleRuleEdit_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearShapeStyleRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建形状样式规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnNewFontNameRuleEdit_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearFontNameRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建字体名称规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnNewFontStyleRuleEdit_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearFontStyleRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建字体样式规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnNewColorRuleEdit_Click(object? sender, EventArgs e)
        {
            try
            {
                ClearColorRuleEditor();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"新建颜色规则失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除图片替换规则
        /// </summary>
        private void BtnDeleteImageRule_Click(object? sender, EventArgs e)
        {
            BtnDeleteImageReplacementRule_Click(sender, e);
        }



        /// <summary>
        /// 删除形状样式替换规则
        /// </summary>
        private void BtnDeleteShapeStyleRule_Click(object? sender, EventArgs e)
        {
            BtnDeleteShapeStyleReplacementRule_Click(sender, e);
        }

        /// <summary>
        /// 浏览源图片
        /// </summary>
        private void BtnBrowseSourceImage_Click(object? sender, EventArgs e)
        {
            try
            {
                using var openFileDialog = new OpenFileDialog
                {
                    Title = "选择源图片",
                    Filter = "图片文件 (*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff;*.webp)|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff;*.webp|所有文件 (*.*)|*.*",
                    DefaultExt = "jpg"
                };

                if (openFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var txtSourceImage = FindControlInPanel<TextBox>("ShapeReplacement", "txtSourceImagePath");
                    if (txtSourceImage != null)
                    {
                        txtSourceImage.Text = openFileDialog.FileName;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择源图片失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 浏览目标图片
        /// </summary>
        private void BtnBrowseTargetImage_Click(object? sender, EventArgs e)
        {
            try
            {
                using var openFileDialog = new OpenFileDialog
                {
                    Title = "选择目标图片",
                    Filter = "图片文件 (*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff;*.webp)|*.jpg;*.jpeg;*.png;*.bmp;*.gif;*.tiff;*.webp|所有文件 (*.*)|*.*",
                    DefaultExt = "jpg"
                };

                if (openFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    var txtTargetImage = FindControlInPanel<TextBox>("ShapeReplacement", "txtTargetImagePath");
                    if (txtTargetImage != null)
                    {
                        txtTargetImage.Text = openFileDialog.FileName;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择目标图片失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 将颜色转换为十六进制字符串，确保始终返回#RRGGBB格式
        /// </summary>
        /// <param name="color">要转换的颜色</param>
        /// <returns>十六进制颜色字符串</returns>
        private static string ColorToHexString(Color color)
        {
            // 使用String.Format确保始终返回#RRGGBB格式，而不是颜色名称
            return $"#{color.R:X2}{color.G:X2}{color.B:X2}";
        }

        #endregion
    }
}
