using System;
using System.IO;
using PPTPiliangChuli.Services;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli
{
    /// <summary>
    /// 配置系统测试类 - 用于验证配置保存加载流程是否正常
    /// </summary>
    public class ConfigTest
    {
        /// <summary>
        /// 运行配置系统测试
        /// </summary>
        public static void RunConfigTest()
        {
            Console.WriteLine("=== PPT批量处理软件配置系统测试 ===");
            Console.WriteLine();

            try
            {
                // 测试1：基本配置加载
                Console.WriteLine("测试1：基本配置加载");
                TestBasicConfigLoading();
                Console.WriteLine("✓ 基本配置加载测试通过");
                Console.WriteLine();

                // 测试2：配置保存和重新加载
                Console.WriteLine("测试2：配置保存和重新加载");
                TestConfigSaveAndReload();
                Console.WriteLine("✓ 配置保存和重新加载测试通过");
                Console.WriteLine();

                // 测试3：功能启用状态测试
                Console.WriteLine("测试3：功能启用状态测试");
                TestFunctionEnabledStatus();
                Console.WriteLine("✓ 功能启用状态测试通过");
                Console.WriteLine();

                // 测试4：PPT格式设置测试
                Console.WriteLine("测试4：PPT格式设置测试");
                TestPPTFormatSettings();
                Console.WriteLine("✓ PPT格式设置测试通过");
                Console.WriteLine();

                // 测试5：配置文件路径映射测试
                Console.WriteLine("测试5：配置文件路径映射测试");
                TestConfigFileMapping();
                Console.WriteLine("✓ 配置文件路径映射测试通过");
                Console.WriteLine();

                Console.WriteLine("=== 所有测试通过！配置系统工作正常 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试基本配置加载
        /// </summary>
        private static void TestBasicConfigLoading()
        {
            var config = ConfigService.Instance.GetConfig();
            
            // 验证配置对象不为空
            if (config == null)
                throw new Exception("配置对象为空");

            // 验证各个配置组件不为空
            if (config.PathSettings == null)
                throw new Exception("路径设置为空");
            
            if (config.ProcessSettings == null)
                throw new Exception("处理设置为空");
            
            if (config.LogSettings == null)
                throw new Exception("日志设置为空");
            
            if (config.FunctionEnabled == null)
                throw new Exception("功能启用状态为空");

            Console.WriteLine($"  - 路径设置: ✓");
            Console.WriteLine($"  - 处理设置: ✓");
            Console.WriteLine($"  - 日志设置: ✓");
            Console.WriteLine($"  - 功能启用状态: ✓");
        }

        /// <summary>
        /// 测试配置保存和重新加载
        /// </summary>
        private static void TestConfigSaveAndReload()
        {
            var config = ConfigService.Instance.GetConfig();
            
            // 修改一些设置
            string originalSourcePath = config.PathSettings.SourcePath;
            string testSourcePath = @"C:\Test\Source";
            config.PathSettings.SourcePath = testSourcePath;
            
            int originalThreadCount = config.ProcessSettings.ThreadCount;
            int testThreadCount = 8;
            config.ProcessSettings.ThreadCount = testThreadCount;

            // 保存配置
            ConfigService.Instance.UpdateConfig(config);

            // 重新加载配置
            ConfigService.Instance.LoadConfig();
            var reloadedConfig = ConfigService.Instance.GetConfig();

            // 验证修改是否保存成功
            if (reloadedConfig.PathSettings.SourcePath != testSourcePath)
                throw new Exception($"源路径保存失败: 期望 {testSourcePath}, 实际 {reloadedConfig.PathSettings.SourcePath}");
            
            if (reloadedConfig.ProcessSettings.ThreadCount != testThreadCount)
                throw new Exception($"线程数保存失败: 期望 {testThreadCount}, 实际 {reloadedConfig.ProcessSettings.ThreadCount}");

            // 恢复原始设置
            config.PathSettings.SourcePath = originalSourcePath;
            config.ProcessSettings.ThreadCount = originalThreadCount;
            ConfigService.Instance.UpdateConfig(config);

            Console.WriteLine($"  - 路径设置保存: ✓");
            Console.WriteLine($"  - 处理设置保存: ✓");
        }

        /// <summary>
        /// 测试功能启用状态
        /// </summary>
        private static void TestFunctionEnabledStatus()
        {
            var functionEnabled = ConfigService.Instance.GetFunctionEnabled();
            
            // 验证所有预期的功能都存在
            string[] expectedFunctions = {
                "页面设置", "内容删除设置", "内容替换设置",
                "PPT格式设置", "匹配段落格式", "页眉页脚设置",
                "文档属性", "文件名替换", "PPT格式转换"
            };

            foreach (string functionName in expectedFunctions)
            {
                if (!functionEnabled.ContainsKey(functionName))
                    throw new Exception($"缺少功能: {functionName}");
                Console.WriteLine($"  - {functionName}: ✓");
            }

            // 测试功能状态修改
            bool originalState = functionEnabled["页面设置"];
            functionEnabled["页面设置"] = !originalState;
            ConfigService.Instance.SaveFunctionEnabled(functionEnabled);

            // 重新加载并验证
            var reloadedFunctionEnabled = ConfigService.Instance.GetFunctionEnabled();
            if (reloadedFunctionEnabled["页面设置"] != !originalState)
                throw new Exception("功能状态修改保存失败");

            // 恢复原始状态
            functionEnabled["页面设置"] = originalState;
            ConfigService.Instance.SaveFunctionEnabled(functionEnabled);
        }

        /// <summary>
        /// 测试PPT格式设置
        /// </summary>
        private static void TestPPTFormatSettings()
        {
            var pptFormatSettings = ConfigService.Instance.GetPPTFormatSettings();
            
            if (pptFormatSettings == null)
                throw new Exception("PPT格式设置为空");

            Console.WriteLine($"  - PPT格式设置加载: ✓");

            // 测试保存PPT格式设置
            var testSettings = new PPTFormatSettings();
            ConfigService.Instance.SavePPTFormatSettings(testSettings);
            
            var reloadedSettings = ConfigService.Instance.GetPPTFormatSettings();
            if (reloadedSettings == null)
                throw new Exception("PPT格式设置保存后重新加载失败");

            Console.WriteLine($"  - PPT格式设置保存: ✓");
        }

        /// <summary>
        /// 测试配置文件路径映射
        /// </summary>
        private static void TestConfigFileMapping()
        {
            string configDir = Path.Combine(System.Windows.Forms.Application.StartupPath, "Config");
            
            // 验证关键配置文件是否存在
            string[] configFiles = {
                "AppConfig.json",
                "PathConfig.json", 
                "ProcessConfig.json",
                "LogConfig.json",
                "PPTFormatSettingsConfig.json"
            };

            foreach (string fileName in configFiles)
            {
                string filePath = Path.Combine(configDir, fileName);
                if (!File.Exists(filePath))
                    throw new Exception($"配置文件不存在: {fileName}");
                Console.WriteLine($"  - {fileName}: ✓");
            }
        }
    }
}
