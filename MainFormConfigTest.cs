using System;
using System.Windows.Forms;
using PPTPiliangChuli.Services;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli
{
    /// <summary>
    /// 主窗体配置测试类 - 用于测试路径设置和处理设置的保存加载功能
    /// </summary>
    public class MainFormConfigTest
    {
        /// <summary>
        /// 运行主窗体配置测试
        /// </summary>
        public static void RunMainFormConfigTest()
        {
            Console.WriteLine("=== 主窗体配置保存加载测试 ===");
            Console.WriteLine();

            try
            {
                // 测试1：路径设置保存加载
                Console.WriteLine("测试1：路径设置保存加载");
                TestPathSettingsSaveLoad();
                Console.WriteLine("✓ 路径设置保存加载测试通过");
                Console.WriteLine();

                // 测试2：处理设置保存加载
                Console.WriteLine("测试2：处理设置保存加载");
                TestProcessSettingsSaveLoad();
                Console.WriteLine("✓ 处理设置保存加载测试通过");
                Console.WriteLine();

                // 测试3：复选框状态测试
                Console.WriteLine("测试3：复选框状态测试");
                TestCheckBoxStates();
                Console.WriteLine("✓ 复选框状态测试通过");
                Console.WriteLine();

                // 测试4：配置文件结构验证
                Console.WriteLine("测试4：配置文件结构验证");
                TestConfigFileStructure();
                Console.WriteLine("✓ 配置文件结构验证通过");
                Console.WriteLine();

                // 测试5：配置同步测试
                Console.WriteLine("测试5：配置同步测试");
                TestConfigSynchronization();
                Console.WriteLine("✓ 配置同步测试通过");
                Console.WriteLine();

                Console.WriteLine("=== 所有主窗体配置测试通过！ ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 测试路径设置保存加载
        /// </summary>
        private static void TestPathSettingsSaveLoad()
        {
            var configService = ConfigService.Instance;
            
            // 获取当前配置
            var config = configService.GetConfig();
            
            // 保存原始值
            string originalSourcePath = config.PathSettings.SourcePath;
            string originalOutputPath = config.PathSettings.OutputPath;
            bool originalIncludeSubfolders = config.PathSettings.IncludeSubfolders;
            bool originalKeepStructure = config.PathSettings.KeepDirectoryStructure;

            try
            {
                // 设置测试值
                string testSourcePath = @"C:\Test\Source";
                string testOutputPath = @"C:\Test\Output";
                bool testIncludeSubfolders = true;
                bool testKeepStructure = true;

                // 修改配置
                config.PathSettings.SourcePath = testSourcePath;
                config.PathSettings.OutputPath = testOutputPath;
                config.PathSettings.IncludeSubfolders = testIncludeSubfolders;
                config.PathSettings.KeepDirectoryStructure = testKeepStructure;

                // 保存配置
                configService.SavePathSettings(config.PathSettings);

                // 重新加载配置
                configService.LoadConfig();
                var reloadedConfig = configService.GetConfig();

                // 验证保存是否成功
                if (reloadedConfig.PathSettings.SourcePath != testSourcePath)
                    throw new Exception($"源路径保存失败: 期望 {testSourcePath}, 实际 {reloadedConfig.PathSettings.SourcePath}");

                if (reloadedConfig.PathSettings.OutputPath != testOutputPath)
                    throw new Exception($"输出路径保存失败: 期望 {testOutputPath}, 实际 {reloadedConfig.PathSettings.OutputPath}");

                if (reloadedConfig.PathSettings.IncludeSubfolders != testIncludeSubfolders)
                    throw new Exception($"包含子目录保存失败: 期望 {testIncludeSubfolders}, 实际 {reloadedConfig.PathSettings.IncludeSubfolders}");

                if (reloadedConfig.PathSettings.KeepDirectoryStructure != testKeepStructure)
                    throw new Exception($"保持目录结构保存失败: 期望 {testKeepStructure}, 实际 {reloadedConfig.PathSettings.KeepDirectoryStructure}");

                Console.WriteLine($"  - 源路径: {testSourcePath} ✓");
                Console.WriteLine($"  - 输出路径: {testOutputPath} ✓");
                Console.WriteLine($"  - 包含子目录: {testIncludeSubfolders} ✓");
                Console.WriteLine($"  - 保持目录结构: {testKeepStructure} ✓");
            }
            finally
            {
                // 恢复原始值
                config.PathSettings.SourcePath = originalSourcePath;
                config.PathSettings.OutputPath = originalOutputPath;
                config.PathSettings.IncludeSubfolders = originalIncludeSubfolders;
                config.PathSettings.KeepDirectoryStructure = originalKeepStructure;
                configService.SavePathSettings(config.PathSettings);
            }
        }

        /// <summary>
        /// 测试处理设置保存加载
        /// </summary>
        private static void TestProcessSettingsSaveLoad()
        {
            var configService = ConfigService.Instance;
            var config = configService.GetConfig();

            // 保存原始值
            bool originalCopyFiles = config.ProcessSettings.CopyFiles;
            bool originalProcessSourceDirectly = config.ProcessSettings.ProcessSourceDirectly;
            int originalThreadCount = config.ProcessSettings.ThreadCount;
            int originalRetryCount = config.ProcessSettings.RetryCount;
            int originalBatchSize = config.ProcessSettings.BatchSize;

            try
            {
                // 设置测试值
                bool testCopyFiles = false; // 移动模式
                bool testProcessSourceDirectly = true;
                int testThreadCount = 8;
                int testRetryCount = 5;
                int testBatchSize = 100;

                // 修改配置
                config.ProcessSettings.CopyFiles = testCopyFiles;
                config.ProcessSettings.ProcessSourceDirectly = testProcessSourceDirectly;
                config.ProcessSettings.ThreadCount = testThreadCount;
                config.ProcessSettings.RetryCount = testRetryCount;
                config.ProcessSettings.BatchSize = testBatchSize;

                // 保存配置
                configService.SaveProcessSettings(config.ProcessSettings);

                // 重新加载配置
                configService.LoadConfig();
                var reloadedConfig = configService.GetConfig();

                // 验证保存是否成功
                if (reloadedConfig.ProcessSettings.CopyFiles != testCopyFiles)
                    throw new Exception($"复制文件模式保存失败: 期望 {testCopyFiles}, 实际 {reloadedConfig.ProcessSettings.CopyFiles}");

                if (reloadedConfig.ProcessSettings.ProcessSourceDirectly != testProcessSourceDirectly)
                    throw new Exception($"直接处理源文件保存失败: 期望 {testProcessSourceDirectly}, 实际 {reloadedConfig.ProcessSettings.ProcessSourceDirectly}");

                if (reloadedConfig.ProcessSettings.ThreadCount != testThreadCount)
                    throw new Exception($"线程数保存失败: 期望 {testThreadCount}, 实际 {reloadedConfig.ProcessSettings.ThreadCount}");

                if (reloadedConfig.ProcessSettings.RetryCount != testRetryCount)
                    throw new Exception($"重试次数保存失败: 期望 {testRetryCount}, 实际 {reloadedConfig.ProcessSettings.RetryCount}");

                if (reloadedConfig.ProcessSettings.BatchSize != testBatchSize)
                    throw new Exception($"批处理大小保存失败: 期望 {testBatchSize}, 实际 {reloadedConfig.ProcessSettings.BatchSize}");

                Console.WriteLine($"  - 复制文件模式: {testCopyFiles} ✓");
                Console.WriteLine($"  - 直接处理源文件: {testProcessSourceDirectly} ✓");
                Console.WriteLine($"  - 线程数: {testThreadCount} ✓");
                Console.WriteLine($"  - 重试次数: {testRetryCount} ✓");
                Console.WriteLine($"  - 批处理大小: {testBatchSize} ✓");
            }
            finally
            {
                // 恢复原始值
                config.ProcessSettings.CopyFiles = originalCopyFiles;
                config.ProcessSettings.ProcessSourceDirectly = originalProcessSourceDirectly;
                config.ProcessSettings.ThreadCount = originalThreadCount;
                config.ProcessSettings.RetryCount = originalRetryCount;
                config.ProcessSettings.BatchSize = originalBatchSize;
                configService.SaveProcessSettings(config.ProcessSettings);
            }
        }

        /// <summary>
        /// 测试复选框状态（模拟复选框同时勾选的情况）
        /// </summary>
        private static void TestCheckBoxStates()
        {
            var configService = ConfigService.Instance;
            var config = configService.GetConfig();

            // 保存原始值
            bool originalIncludeSubfolders = config.PathSettings.IncludeSubfolders;
            bool originalKeepStructure = config.PathSettings.KeepDirectoryStructure;

            try
            {
                // 测试同时勾选两个复选框
                config.PathSettings.IncludeSubfolders = true;
                config.PathSettings.KeepDirectoryStructure = true;

                // 保存配置
                configService.SavePathSettings(config.PathSettings);

                // 重新加载配置
                configService.LoadConfig();
                var reloadedConfig = configService.GetConfig();

                // 验证两个复选框都能同时为true
                if (!reloadedConfig.PathSettings.IncludeSubfolders)
                    throw new Exception("包含子目录复选框无法保持勾选状态");

                if (!reloadedConfig.PathSettings.KeepDirectoryStructure)
                    throw new Exception("保持目录结构复选框无法保持勾选状态");

                Console.WriteLine($"  - 包含子目录: {reloadedConfig.PathSettings.IncludeSubfolders} ✓");
                Console.WriteLine($"  - 保持目录结构: {reloadedConfig.PathSettings.KeepDirectoryStructure} ✓");
                Console.WriteLine($"  - 两个复选框可以同时勾选 ✓");

                // 测试同时取消勾选
                config.PathSettings.IncludeSubfolders = false;
                config.PathSettings.KeepDirectoryStructure = false;
                configService.SavePathSettings(config.PathSettings);
                configService.LoadConfig();
                reloadedConfig = configService.GetConfig();

                if (reloadedConfig.PathSettings.IncludeSubfolders)
                    throw new Exception("包含子目录复选框无法取消勾选");

                if (reloadedConfig.PathSettings.KeepDirectoryStructure)
                    throw new Exception("保持目录结构复选框无法取消勾选");

                Console.WriteLine($"  - 两个复选框可以同时取消勾选 ✓");
            }
            finally
            {
                // 恢复原始值
                config.PathSettings.IncludeSubfolders = originalIncludeSubfolders;
                config.PathSettings.KeepDirectoryStructure = originalKeepStructure;
                configService.SavePathSettings(config.PathSettings);
            }
        }

        /// <summary>
        /// 测试配置文件结构验证
        /// </summary>
        private static void TestConfigFileStructure()
        {
            var configService = ConfigService.Instance;

            // 测试PathSettings结构
            var pathSettings = configService.GetPathSettings();
            if (pathSettings == null)
                throw new Exception("PathSettings加载失败");

            // 验证PathSettings属性
            var pathType = pathSettings.GetType();
            if (pathType.GetProperty("SourcePath") == null)
                throw new Exception("PathSettings缺少SourcePath属性");
            if (pathType.GetProperty("OutputPath") == null)
                throw new Exception("PathSettings缺少OutputPath属性");
            if (pathType.GetProperty("IncludeSubfolders") == null)
                throw new Exception("PathSettings缺少IncludeSubfolders属性");
            if (pathType.GetProperty("KeepDirectoryStructure") == null)
                throw new Exception("PathSettings缺少KeepDirectoryStructure属性");

            Console.WriteLine($"  - PathSettings结构验证: ✓");

            // 测试ProcessSettings结构
            var processSettings = configService.GetProcessSettings();
            if (processSettings == null)
                throw new Exception("ProcessSettings加载失败");

            // 验证ProcessSettings属性
            var processType = processSettings.GetType();
            if (processType.GetProperty("CopyFiles") == null)
                throw new Exception("ProcessSettings缺少CopyFiles属性");
            if (processType.GetProperty("ProcessSourceDirectly") == null)
                throw new Exception("ProcessSettings缺少ProcessSourceDirectly属性");
            if (processType.GetProperty("ThreadCount") == null)
                throw new Exception("ProcessSettings缺少ThreadCount属性");

            Console.WriteLine($"  - ProcessSettings结构验证: ✓");
        }

        /// <summary>
        /// 测试配置同步
        /// </summary>
        private static void TestConfigSynchronization()
        {
            var configService = ConfigService.Instance;

            // 通过不同方法获取配置，验证是否同步
            var config1 = configService.GetConfig();
            var pathSettings = configService.GetPathSettings();
            var processSettings = configService.GetProcessSettings();

            // 验证配置对象的一致性
            if (config1.PathSettings.SourcePath != pathSettings.SourcePath)
                throw new Exception("PathSettings同步失败");

            if (config1.ProcessSettings.ThreadCount != processSettings.ThreadCount)
                throw new Exception("ProcessSettings同步失败");

            Console.WriteLine($"  - 配置对象同步验证: ✓");

            // 测试配置更新后的同步
            string testPath = @"C:\Test\Sync";
            config1.PathSettings.SourcePath = testPath;
            configService.UpdateConfig(config1);

            var config2 = configService.GetConfig();
            if (config2.PathSettings.SourcePath != testPath)
                throw new Exception("配置更新同步失败");

            Console.WriteLine($"  - 配置更新同步验证: ✓");
        }
    }
}
