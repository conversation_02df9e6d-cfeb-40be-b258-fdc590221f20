using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Windows.Forms;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli
{
    /// <summary>
    /// 配置诊断和修复工具类 - 用于检查和修复配置系统问题
    /// </summary>
    public class ConfigDiagnostics
    {
        /// <summary>
        /// 运行完整的配置诊断
        /// </summary>
        public static List<string> RunFullDiagnostics()
        {
            var issues = new List<string>();
            
            Console.WriteLine("=== 配置系统诊断开始 ===");
            
            // 检查配置文件存在性
            issues.AddRange(CheckConfigFileExistence());
            
            // 检查配置文件格式
            issues.AddRange(CheckConfigFileFormat());
            
            // 检查功能名称一致性
            issues.AddRange(CheckFunctionNameConsistency());
            
            // 检查配置文件路径映射
            issues.AddRange(CheckConfigFileMapping());
            
            // 检查配置加载
            issues.AddRange(CheckConfigLoading());
            
            Console.WriteLine($"=== 配置系统诊断完成，发现 {issues.Count} 个问题 ===");
            
            return issues;
        }

        /// <summary>
        /// 检查配置文件存在性
        /// </summary>
        private static List<string> CheckConfigFileExistence()
        {
            var issues = new List<string>();
            string configDir = Path.Combine(Application.StartupPath, "Config");
            
            Console.WriteLine("检查配置文件存在性...");
            
            var requiredFiles = new Dictionary<string, string>
            {
                { "AppConfig.json", "主配置文件" },
                { "PathConfig.json", "路径配置文件" },
                { "ProcessConfig.json", "处理配置文件" },
                { "LogConfig.json", "日志配置文件" },
                { "FormConfig.json", "窗体配置文件" },
                { "PageSetupConfig.json", "页面设置配置文件" },
                { "BackgroundConfig.json", "背景配置文件" },
                { "ContentDeletionConfig.json", "内容删除配置文件" },
                { "ContentReplacementConfig.json", "内容替换配置文件" },
                { "ParagraphFormatConfig.json", "段落格式配置文件" },
                { "HeaderFooterConfig.json", "页眉页脚配置文件" },
                { "DocumentPropertiesConfig.json", "文档属性配置文件" },
                { "FilenameReplacementConfig.json", "文件名替换配置文件" },
                { "PPTFormatConversionConfig.json", "PPT格式转换配置文件" },
                { "PPTFormatSettingsConfig.json", "PPT格式设置配置文件" },
                { "ScheduleConfig.json", "定时配置文件" },
                { "FilenameIllegalWordsConfig.json", "文件名非法词配置文件" },
                { "ContentIllegalWordsConfig.json", "内容非法词配置文件" }
            };

            foreach (var file in requiredFiles)
            {
                string filePath = Path.Combine(configDir, file.Key);
                if (!File.Exists(filePath))
                {
                    issues.Add($"缺少{file.Value}: {file.Key}");
                    Console.WriteLine($"  ❌ 缺少: {file.Key}");
                }
                else
                {
                    Console.WriteLine($"  ✓ 存在: {file.Key}");
                }
            }
            
            return issues;
        }

        /// <summary>
        /// 检查配置文件格式
        /// </summary>
        private static List<string> CheckConfigFileFormat()
        {
            var issues = new List<string>();
            string configDir = Path.Combine(Application.StartupPath, "Config");
            
            Console.WriteLine("检查配置文件格式...");
            
            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                AllowTrailingCommas = true,
                ReadCommentHandling = JsonCommentHandling.Skip
            };

            foreach (string file in Directory.GetFiles(configDir, "*.json"))
            {
                try
                {
                    string content = File.ReadAllText(file);
                    JsonDocument.Parse(content, new JsonDocumentOptions
                    {
                        AllowTrailingCommas = true,
                        CommentHandling = JsonCommentHandling.Skip
                    });
                    Console.WriteLine($"  ✓ 格式正确: {Path.GetFileName(file)}");
                }
                catch (JsonException ex)
                {
                    issues.Add($"JSON格式错误 {Path.GetFileName(file)}: {ex.Message}");
                    Console.WriteLine($"  ❌ 格式错误: {Path.GetFileName(file)} - {ex.Message}");
                }
                catch (Exception ex)
                {
                    issues.Add($"文件读取错误 {Path.GetFileName(file)}: {ex.Message}");
                    Console.WriteLine($"  ❌ 读取错误: {Path.GetFileName(file)} - {ex.Message}");
                }
            }
            
            return issues;
        }

        /// <summary>
        /// 检查功能名称一致性
        /// </summary>
        private static List<string> CheckFunctionNameConsistency()
        {
            var issues = new List<string>();
            
            Console.WriteLine("检查功能名称一致性...");
            
            try
            {
                string appConfigPath = Path.Combine(Application.StartupPath, "Config", "AppConfig.json");
                if (File.Exists(appConfigPath))
                {
                    string content = File.ReadAllText(appConfigPath);
                    var jsonOptions = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        AllowTrailingCommas = true,
                        ReadCommentHandling = JsonCommentHandling.Skip
                    };
                    
                    var appConfig = JsonSerializer.Deserialize<Dictionary<string, object>>(content, jsonOptions);
                    
                    if (appConfig != null && appConfig.TryGetValue("FunctionEnabled", out object? functionEnabledObj))
                    {
                        var functionEnabledJson = JsonSerializer.Serialize(functionEnabledObj, jsonOptions);
                        var functionEnabled = JsonSerializer.Deserialize<Dictionary<string, bool>>(functionEnabledJson, jsonOptions);
                        
                        if (functionEnabled != null)
                        {
                            var expectedFunctions = new string[]
                            {
                                "页面设置", "内容删除设置", "内容替换设置",
                                "PPT格式设置", "匹配段落格式", "页眉页脚设置",
                                "文档属性", "文件名替换", "PPT格式转换"
                            };

                            foreach (string expectedFunction in expectedFunctions)
                            {
                                if (functionEnabled.ContainsKey(expectedFunction))
                                {
                                    Console.WriteLine($"  ✓ 功能名称正确: {expectedFunction}");
                                }
                                else
                                {
                                    issues.Add($"缺少功能定义: {expectedFunction}");
                                    Console.WriteLine($"  ❌ 缺少功能: {expectedFunction}");
                                }
                            }

                            // 检查是否有多余的功能定义
                            foreach (string actualFunction in functionEnabled.Keys)
                            {
                                if (!Array.Exists(expectedFunctions, f => f == actualFunction))
                                {
                                    issues.Add($"未知功能定义: {actualFunction}");
                                    Console.WriteLine($"  ⚠️ 未知功能: {actualFunction}");
                                }
                            }
                        }
                    }
                    else
                    {
                        issues.Add("AppConfig.json中缺少FunctionEnabled配置");
                        Console.WriteLine("  ❌ 缺少FunctionEnabled配置");
                    }
                }
            }
            catch (Exception ex)
            {
                issues.Add($"检查功能名称一致性时出错: {ex.Message}");
                Console.WriteLine($"  ❌ 检查出错: {ex.Message}");
            }
            
            return issues;
        }

        /// <summary>
        /// 检查配置文件路径映射
        /// </summary>
        private static List<string> CheckConfigFileMapping()
        {
            var issues = new List<string>();
            
            Console.WriteLine("检查配置文件路径映射...");
            
            // 这里我们检查ConfigService中定义的文件路径是否与实际文件匹配
            var expectedMappings = new Dictionary<string, string>
            {
                { "PPTFormat", "PPTFormatSettingsConfig.json" },
                { "App", "AppConfig.json" },
                { "Path", "PathConfig.json" },
                { "Process", "ProcessConfig.json" },
                { "Log", "LogConfig.json" }
            };

            string configDir = Path.Combine(Application.StartupPath, "Config");
            
            foreach (var mapping in expectedMappings)
            {
                string expectedFile = Path.Combine(configDir, mapping.Value);
                if (File.Exists(expectedFile))
                {
                    Console.WriteLine($"  ✓ 映射正确: {mapping.Key} -> {mapping.Value}");
                }
                else
                {
                    issues.Add($"配置文件映射错误: {mapping.Key} -> {mapping.Value} (文件不存在)");
                    Console.WriteLine($"  ❌ 映射错误: {mapping.Key} -> {mapping.Value}");
                }
            }
            
            return issues;
        }

        /// <summary>
        /// 检查配置加载
        /// </summary>
        private static List<string> CheckConfigLoading()
        {
            var issues = new List<string>();
            
            Console.WriteLine("检查配置加载...");
            
            try
            {
                var config = ConfigService.Instance.GetConfig();
                
                if (config == null)
                {
                    issues.Add("配置加载失败: 返回null");
                    Console.WriteLine("  ❌ 配置加载失败");
                    return issues;
                }

                // 检查各个配置组件
                var components = new Dictionary<string, object>
                {
                    { "PathSettings", config.PathSettings },
                    { "ProcessSettings", config.ProcessSettings },
                    { "LogSettings", config.LogSettings },
                    { "FormSettings", config.FormSettings },
                    { "FunctionEnabled", config.FunctionEnabled }
                };

                foreach (var component in components)
                {
                    if (component.Value == null)
                    {
                        issues.Add($"配置组件为空: {component.Key}");
                        Console.WriteLine($"  ❌ 组件为空: {component.Key}");
                    }
                    else
                    {
                        Console.WriteLine($"  ✓ 组件正常: {component.Key}");
                    }
                }
            }
            catch (Exception ex)
            {
                issues.Add($"配置加载异常: {ex.Message}");
                Console.WriteLine($"  ❌ 加载异常: {ex.Message}");
            }
            
            return issues;
        }

        /// <summary>
        /// 自动修复配置问题
        /// </summary>
        public static bool AutoFixConfigIssues()
        {
            Console.WriteLine("=== 开始自动修复配置问题 ===");
            
            try
            {
                // 重新初始化ConfigService，这会自动创建缺失的配置文件
                var config = ConfigService.Instance.GetConfig();
                
                Console.WriteLine("✓ 配置自动修复完成");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 配置自动修复失败: {ex.Message}");
                return false;
            }
        }
    }
}
