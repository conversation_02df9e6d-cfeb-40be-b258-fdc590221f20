// PPT内容替换设置窗体
// 功能：配置PPT内容替换的各种选项，包括文本替换、形状替换、字体替换、颜色替换等4个分类
// 作者：PPT批量处理工具开发团队
// 创建时间：2024年
// 最后修改：2024年

using PPTPiliangChuli.Models;
using PPTPiliangChuli.Services;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 内容替换设置窗体
    /// </summary>
    public partial class ContentReplacementForm : Form
    {
        // 当前设置
        private ContentReplacementSettings _currentSettings = new();

        // 各标签页的控件容器
        private readonly Dictionary<string, Panel> _tabPanels = new();

        public ContentReplacementForm()
        {
            InitializeComponent();
            InitializeSettings();
            InitializeTabPages();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化设置
        /// </summary>
        private void InitializeSettings()
        {
            try
            {
                var config = ConfigService.Instance.GetConfig();
                _currentSettings = config.ContentReplacementSettings;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载内容替换设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _currentSettings = new ContentReplacementSettings();
            }
        }

        /// <summary>
        /// 初始化标签页
        /// </summary>
        private void InitializeTabPages()
        {
            try
            {
                // 初始化文本替换标签页
                InitializeTextReplacementTab();

                // 初始化形状替换标签页
                InitializeShapeReplacementTab();

                // 初始化字体替换标签页
                InitializeFontReplacementTab();

                // 初始化颜色替换标签页
                InitializeColorReplacementTab();

                // 加载当前设置到界面
                LoadSettingsToUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化标签页失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 创建替换范围区域 - 通用方法，用于所有标签页
        /// </summary>
        private void CreateReplacementScopeSection(Panel parentPanel, string tabType, ref int yPos)
        {
            // 替换范围设置区域
            var grpReplacementScope = CreateGroupBox("替换范围", 25, yPos, 1140, 120);

            // 创建自定义尺寸的复选框，避免重叠
            var chkIncludeNormalSlides = new CheckBox
            {
                Text = "普通幻灯片页",
                Location = new Point(30, 45),
                Size = new Size(200, 45), // 设置合适的宽度
                UseVisualStyleBackColor = true,
                AutoSize = false,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                TextAlign = ContentAlignment.MiddleLeft,
                Name = $"chkIncludeNormalSlides_{tabType}",
                Checked = true // 默认选中
            };

            var chkIncludeMasterSlides = new CheckBox
            {
                Text = "母版页",
                Location = new Point(280, 45),
                Size = new Size(150, 45), // 设置合适的宽度
                UseVisualStyleBackColor = true,
                AutoSize = false,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                TextAlign = ContentAlignment.MiddleLeft,
                Name = $"chkIncludeMasterSlides_{tabType}",
                Checked = true // 默认选中
            };

            var chkIncludeLayoutSlides = new CheckBox
            {
                Text = "母版版式页",
                Location = new Point(480, 45),
                Size = new Size(200, 45), // 设置合适的宽度
                UseVisualStyleBackColor = true,
                AutoSize = false,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                TextAlign = ContentAlignment.MiddleLeft,
                Name = $"chkIncludeLayoutSlides_{tabType}",
                Checked = true // 默认选中
            };

            grpReplacementScope.Controls.AddRange(new Control[] {
                chkIncludeNormalSlides, chkIncludeMasterSlides, chkIncludeLayoutSlides
            });

            parentPanel.Controls.Add(grpReplacementScope);
            yPos += 140;
        }

        /// <summary>
        /// 初始化文本替换标签页
        /// </summary>
        private void InitializeTextReplacementTab()
        {
            var panel = CreateScrollablePanel();
            tabPageTextReplacement.Controls.Add(panel);
            _tabPanels["TextReplacement"] = panel;

            int yPos = 25;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用文本替换功能", 25, yPos);
            chkMasterSwitch.Name = "chkTextReplacementMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 替换范围区域
            CreateReplacementScopeSection(panel, "TextReplacement", ref yPos);

            // 文本替换类型选项
            var grpReplacementTypes = CreateGroupBox("文本替换类型", 25, yPos, 1140, 180);

            var chkNormalText = CreateCheckBox("普通文本替换", 30, 45);
            chkNormalText.Name = "chkEnableNormalTextReplacement";

            var chkRegexText = CreateCheckBox("正则表达式替换", 30, 90);
            chkRegexText.Name = "chkEnableRegexReplacement";

            var chkBatchText = CreateCheckBox("批量文本替换", 30, 135);
            chkBatchText.Name = "chkEnableBatchTextReplacement";

            var chkRangeText = CreateCheckBox("指定范围替换", 580, 45);
            chkRangeText.Name = "chkEnableRangeReplacement";

            grpReplacementTypes.Controls.AddRange(new Control[] { chkNormalText, chkRegexText, chkBatchText, chkRangeText });
            panel.Controls.Add(grpReplacementTypes);
            yPos += 200;

            // 替换范围设置
            var grpReplacementRange = CreateGroupBox("替换范围设置", 25, yPos, 1140, 180);

            var chkReplaceInTitles = CreateCheckBox("替换标题文本", 30, 45);
            chkReplaceInTitles.Name = "chkReplaceInTitles";

            var chkReplaceInContent = CreateCheckBox("替换内容文本", 30, 90);
            chkReplaceInContent.Name = "chkReplaceInContent";

            var chkReplaceInNotes = CreateCheckBox("替换备注文本", 30, 135);
            chkReplaceInNotes.Name = "chkReplaceInNotes";

            var chkReplaceInTables = CreateCheckBox("替换表格文本", 580, 45);
            chkReplaceInTables.Name = "chkReplaceInTables";

            var chkReplaceInCharts = CreateCheckBox("替换图表文本", 580, 90);
            chkReplaceInCharts.Name = "chkReplaceInCharts";

            grpReplacementRange.Controls.AddRange(new Control[] {
                chkReplaceInTitles, chkReplaceInContent, chkReplaceInNotes,
                chkReplaceInTables, chkReplaceInCharts
            });
            panel.Controls.Add(grpReplacementRange);
            yPos += 200;

            // 文本替换规则编辑区域
            CreateTextReplacementRuleEditSection(panel, ref yPos);

            // 导入说明
            var grpImportInstructions = CreateGroupBox("导入说明", 25, yPos, 1140, 160);

            var lblInstructions = new Label
            {
                Text = "Excel导入说明：\n" +
                       "1. 点击\"下载模板\"按钮下载Excel模板文件\n" +
                       "2. 在模板中填写替换规则，每行一条规则\n" +
                       "3. 保存Excel文件后，点击\"导入Excel\"按钮选择文件导入\n" +
                       "4. 支持.xlsx和.xls格式的Excel文件",
                Location = new Point(30, 40),
                Size = new Size(1080, 110),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            grpImportInstructions.Controls.Add(lblInstructions);
            panel.Controls.Add(grpImportInstructions);
        }

        /// <summary>
        /// 初始化形状替换标签页
        /// </summary>
        private void InitializeShapeReplacementTab()
        {
            var panel = CreateScrollablePanel();
            tabPageShapeReplacement.Controls.Add(panel);
            _tabPanels["ShapeReplacement"] = panel;

            int yPos = 25;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用形状替换功能", 25, yPos);
            chkMasterSwitch.Name = "chkShapeReplacementMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 替换范围区域
            CreateReplacementScopeSection(panel, "ShapeReplacement", ref yPos);

            // 形状替换类型选项
            var grpShapeTypes = CreateGroupBox("形状替换类型", 25, yPos, 1140, 160);

            var chkImageReplacement = CreateCheckBox("图片替换", 30, 45);
            chkImageReplacement.Name = "chkEnableImageReplacement";

            var chkTextBoxReplacement = CreateCheckBox("文本框替换", 30, 90);
            chkTextBoxReplacement.Name = "chkEnableTextBoxReplacement";

            var chkShapeStyleReplacement = CreateCheckBox("形状样式替换", 580, 45);
            chkShapeStyleReplacement.Name = "chkEnableShapeStyleReplacement";

            grpShapeTypes.Controls.AddRange(new Control[] { chkImageReplacement, chkTextBoxReplacement, chkShapeStyleReplacement });
            panel.Controls.Add(grpShapeTypes);
            yPos += 180;

            // 形状替换规则编辑区域
            CreateShapeReplacementEditSection(panel, ref yPos);
        }



        /// <summary>
        /// 初始化字体替换标签页
        /// </summary>
        private void InitializeFontReplacementTab()
        {
            var panel = CreateScrollablePanel();
            tabPageFontReplacement.Controls.Add(panel);
            _tabPanels["FontReplacement"] = panel;

            int yPos = 25;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用字体替换功能", 25, yPos);
            chkMasterSwitch.Name = "chkFontReplacementMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 替换范围区域
            CreateReplacementScopeSection(panel, "FontReplacement", ref yPos);

            // 字体替换类型选项
            var grpFontTypes = CreateGroupBox("字体替换类型", 25, yPos, 1140, 160);

            var chkFontNameReplacement = CreateCheckBox("字体名称替换", 30, 45);
            chkFontNameReplacement.Name = "chkEnableFontNameReplacement";

            var chkFontStyleReplacement = CreateCheckBox("字体样式替换", 30, 90);
            chkFontStyleReplacement.Name = "chkEnableFontStyleReplacement";

            var chkFontEmbedding = CreateCheckBox("字体嵌入", 580, 45);
            chkFontEmbedding.Name = "chkEnableFontEmbedding";

            grpFontTypes.Controls.AddRange(new Control[] { chkFontNameReplacement, chkFontStyleReplacement, chkFontEmbedding });
            panel.Controls.Add(grpFontTypes);
            yPos += 180;

            // 字体替换规则编辑区域
            CreateFontReplacementEditSection(panel, ref yPos);
        }





        /// <summary>
        /// 初始化颜色替换标签页
        /// </summary>
        private void InitializeColorReplacementTab()
        {
            var panel = CreateScrollablePanel();
            tabPageColorReplacement.Controls.Add(panel);
            _tabPanels["ColorReplacement"] = panel;

            int yPos = 25;

            // 总开关
            var chkMasterSwitch = CreateCheckBox("启用颜色替换功能", 25, yPos);
            chkMasterSwitch.Name = "chkColorReplacementMaster";
            chkMasterSwitch.Font = new Font(chkMasterSwitch.Font, FontStyle.Bold);
            panel.Controls.Add(chkMasterSwitch);
            yPos += 70;

            // 替换范围区域
            CreateReplacementScopeSection(panel, "ColorReplacement", ref yPos);

            // 颜色替换类型选项
            var grpColorTypes = CreateGroupBox("颜色替换类型", 25, yPos, 1140, 140);

            var chkThemeColorReplacement = CreateCheckBox("主题颜色替换", 30, 45);
            chkThemeColorReplacement.Name = "chkEnableThemeColorReplacement";

            var chkCustomColorReplacement = CreateCheckBox("自定义颜色替换", 580, 45);
            chkCustomColorReplacement.Name = "chkEnableCustomColorReplacement";

            grpColorTypes.Controls.AddRange(new Control[] { chkThemeColorReplacement, chkCustomColorReplacement });
            panel.Controls.Add(grpColorTypes);
            yPos += 160;

            // 颜色替换规则编辑区域
            CreateColorReplacementEditSection(panel, ref yPos);
        }



        /// <summary>
        /// 创建文本替换规则编辑区域
        /// </summary>
        private void CreateTextReplacementRuleEditSection(Panel parentPanel, ref int yPos)
        {
            // 上方：规则列表
            var grpRulesList = CreateGroupBox("规则列表", 25, yPos, 1140, 450);

            var listViewRules = new ListView
            {
                Name = "listViewTextReplacementRules",
                Location = new Point(20, 45),
                Size = new Size(1100, 320),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                MultiSelect = false
            };

            // 添加双击事件处理，双击规则时自动加载到编辑区域
            listViewRules.DoubleClick += (sender, e) =>
            {
                if (listViewRules.SelectedItems.Count > 0)
                {
                    LoadTextRuleToEditor(listViewRules.SelectedItems[0]);
                }
            };

            listViewRules.Columns.Add("启用", 60);
            listViewRules.Columns.Add("规则名称", 150);
            listViewRules.Columns.Add("查找文本", 200);
            listViewRules.Columns.Add("替换文本", 200);
            listViewRules.Columns.Add("正则表达式", 100);
            listViewRules.Columns.Add("区分大小写", 100);
            listViewRules.Columns.Add("全词匹配", 100);

            // 规则列表操作按钮
            var btnNewRule = CreateButton("新建", 20, 375, 90);
            btnNewRule.Name = "btnAddTextReplacementRule";

            var btnEditRule = CreateButton("编辑", 120, 375, 90);
            btnEditRule.Name = "btnEditTextReplacementRule";

            var btnDeleteRule = CreateButton("删除", 220, 375, 90);
            btnDeleteRule.Name = "btnDeleteTextReplacementRule";

            var btnImportRules = CreateButton("导入Excel", 320, 375, 110);
            btnImportRules.Name = "btnImportTextReplacementRules";

            var btnExportRules = CreateButton("导出规则", 440, 375, 110);
            btnExportRules.Name = "btnExportTextReplacementRules";

            var btnDownloadTemplate = CreateButton("下载模板", 560, 375, 110);
            btnDownloadTemplate.Name = "btnDownloadExcelTemplate";

            grpRulesList.Controls.AddRange(new Control[] {
                listViewRules, btnNewRule, btnEditRule, btnDeleteRule, btnImportRules, btnExportRules, btnDownloadTemplate
            });

            parentPanel.Controls.Add(grpRulesList);
            yPos += 470;

            // 下方：规则编辑区域
            var grpRuleEdit = CreateGroupBox("规则编辑", 25, yPos, 1140, 480);

            CreateTextRuleEditControls(grpRuleEdit);

            parentPanel.Controls.Add(grpRuleEdit);
            yPos += 500;

            // 设置滚动区域的最小尺寸，确保所有内容都能显示
            parentPanel.AutoScrollMinSize = new Size(1140, yPos + 50);
        }

        /// <summary>
        /// 创建文本规则编辑控件
        /// </summary>
        private void CreateTextRuleEditControls(GroupBox parentGroup)
        {
            int yPos = 45;

            // 第一行：规则名称和应用范围
            var lblRuleName = CreateLabel("规则名称:", 20, yPos);
            var txtRuleName = CreateTextBox(170, yPos, 300, true);
            txtRuleName.Name = "txtTextRuleName";

            var lblRangeType = CreateLabel("应用范围:", 500, yPos);
            var cmbRangeType = CreateComboBox(650, yPos, 250);
            cmbRangeType.Name = "cmbRangeType";
            cmbRangeType.Items.AddRange(new[] { "所有文本", "标题文本", "内容文本", "备注文本", "形状文本" });
            cmbRangeType.SelectedIndex = 0;

            parentGroup.Controls.AddRange(new Control[] { lblRuleName, txtRuleName, lblRangeType, cmbRangeType });
            yPos += 60;

            // 第二行：查找文本
            var lblFindText = CreateLabel("查找文本:", 20, yPos);
            var txtFindText = CreateTextBox(170, yPos, 950, false); // 多行文本框保持左对齐
            txtFindText.Name = "txtFindText";
            txtFindText.Multiline = true;
            txtFindText.Size = new Size(950, 70);
            parentGroup.Controls.AddRange(new Control[] { lblFindText, txtFindText });
            yPos += 90;

            // 第三行：替换文本
            var lblReplaceText = CreateLabel("替换文本:", 20, yPos);
            var txtReplaceText = CreateTextBox(170, yPos, 950, false); // 多行文本框保持左对齐
            txtReplaceText.Name = "txtReplaceText";
            txtReplaceText.Multiline = true;
            txtReplaceText.Size = new Size(950, 70);
            parentGroup.Controls.AddRange(new Control[] { lblReplaceText, txtReplaceText });
            yPos += 90;

            // 第四行：选项设置
            var chkUseRegex = CreateCheckBox("使用正则表达式", 20, yPos);
            chkUseRegex.Name = "chkUseRegex";

            var chkCaseSensitive = CreateCheckBox("区分大小写", 300, yPos);
            chkCaseSensitive.Name = "chkCaseSensitive";

            var chkWholeWord = CreateCheckBox("全词匹配", 580, yPos);
            chkWholeWord.Name = "chkWholeWord";

            var chkIsEnabled = CreateCheckBox("启用此规则", 860, yPos);
            chkIsEnabled.Name = "chkTextRuleEnabled";
            chkIsEnabled.Checked = true;

            parentGroup.Controls.AddRange(new Control[] { chkUseRegex, chkCaseSensitive, chkWholeWord, chkIsEnabled });
            yPos += 60;

            // 第五行：操作按钮
            var btnNewRule = CreateButton("新建规则", 20, yPos, 110);
            btnNewRule.Name = "btnNewTextRuleEdit";

            var btnSaveRule = CreateButton("保存规则", 140, yPos, 110);
            btnSaveRule.Name = "btnSaveTextRule";

            var btnClearRule = CreateButton("清空", 260, yPos, 90);
            btnClearRule.Name = "btnClearTextRule";

            parentGroup.Controls.AddRange(new Control[] { btnNewRule, btnSaveRule, btnClearRule });
        }

        /// <summary>
        /// 创建形状替换编辑区域
        /// </summary>
        private void CreateShapeReplacementEditSection(Panel parentPanel, ref int yPos)
        {
            // 形状类型选择标签页
            var tabControlShapeTypes = new TabControl
            {
                Location = new Point(25, yPos),
                Size = new Size(1140, 700),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            // 图片替换标签页
            var tabPageImage = new TabPage("图片替换");
            CreateImageReplacementEditControls(tabPageImage);
            tabControlShapeTypes.TabPages.Add(tabPageImage);

            // 文本框替换标签页
            var tabPageTextBox = new TabPage("文本框替换");
            CreateTextBoxReplacementEditControls(tabPageTextBox);
            tabControlShapeTypes.TabPages.Add(tabPageTextBox);

            // 形状样式替换标签页
            var tabPageShapeStyle = new TabPage("形状样式替换");
            CreateShapeStyleReplacementEditControls(tabPageShapeStyle);
            tabControlShapeTypes.TabPages.Add(tabPageShapeStyle);

            parentPanel.Controls.Add(tabControlShapeTypes);
            yPos += 720;

            // 设置滚动区域的最小尺寸
            parentPanel.AutoScrollMinSize = new Size(1140, yPos + 50);
        }

        /// <summary>
        /// 创建图片替换编辑控件
        /// </summary>
        private void CreateImageReplacementEditControls(TabPage tabPage)
        {
            // 上方：规则列表
            var grpRulesList = CreateGroupBox("图片替换规则列表", 25, 25, 1090, 280);

            var listViewRules = new ListView
            {
                Name = "listViewImageReplacementRules",
                Location = new Point(20, 45),
                Size = new Size(1050, 160),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            listViewRules.Columns.Add("启用", 60);
            listViewRules.Columns.Add("规则名称", 150);
            listViewRules.Columns.Add("源图片", 300);
            listViewRules.Columns.Add("目标图片", 300);
            listViewRules.Columns.Add("匹配方式", 120);

            var btnNewRule = CreateButton("新建", 20, 215, 90);
            btnNewRule.Name = "btnNewImageRule";

            var btnEditRule = CreateButton("编辑", 120, 215, 90);
            btnEditRule.Name = "btnEditImageRule";

            var btnDeleteRule = CreateButton("删除", 220, 215, 90);
            btnDeleteRule.Name = "btnDeleteImageRule";

            var btnBrowseSource = CreateButton("浏览源图片", 320, 215, 130);
            btnBrowseSource.Name = "btnBrowseSourceImage";

            var btnBrowseTarget = CreateButton("浏览目标图片", 460, 215, 130);
            btnBrowseTarget.Name = "btnBrowseTargetImage";

            grpRulesList.Controls.AddRange(new Control[] {
                listViewRules, btnNewRule, btnEditRule, btnDeleteRule, btnBrowseSource, btnBrowseTarget
            });

            tabPage.Controls.Add(grpRulesList);

            // 下方：规则编辑区域
            var grpRuleEdit = CreateGroupBox("图片替换规则编辑", 25, 325, 1090, 450);

            CreateImageRuleEditControls(grpRuleEdit);

            tabPage.Controls.Add(grpRuleEdit);
        }

        /// <summary>
        /// 创建图片规则编辑控件
        /// </summary>
        private void CreateImageRuleEditControls(GroupBox parentGroup)
        {
            int yPos = 45;

            // 第一行：规则名称和匹配方式
            var lblRuleName = CreateLabel("规则名称:", 20, yPos);
            var txtRuleName = CreateTextBox(170, yPos, 300, true);
            txtRuleName.Name = "txtImageRuleName";

            var lblMatchType = CreateLabel("匹配方式:", 500, yPos);
            var cmbMatchType = CreateComboBox(650, yPos, 250);
            cmbMatchType.Name = "cmbImageMatchType";
            cmbMatchType.Items.AddRange(new[] { "按文件名匹配", "按图片内容匹配", "按位置匹配", "按尺寸匹配" });
            cmbMatchType.SelectedIndex = 0;

            parentGroup.Controls.AddRange(new Control[] { lblRuleName, txtRuleName, lblMatchType, cmbMatchType });
            yPos += 50;

            // 第二行：源图片路径
            var lblSourceImage = CreateLabel("源图片:", 20, yPos);
            var txtSourceImage = CreateTextBox(170, yPos, 650, true);
            txtSourceImage.Name = "txtSourceImagePath";
            var btnBrowseSource = CreateButton("浏览", 830, yPos, 90);
            btnBrowseSource.Name = "btnBrowseSourceImg";
            parentGroup.Controls.AddRange(new Control[] { lblSourceImage, txtSourceImage, btnBrowseSource });
            yPos += 50;

            // 第三行：目标图片路径
            var lblTargetImage = CreateLabel("目标图片:", 20, yPos);
            var txtTargetImage = CreateTextBox(170, yPos, 650, true);
            txtTargetImage.Name = "txtTargetImagePath";
            var btnBrowseTarget = CreateButton("浏览", 830, yPos, 90);
            btnBrowseTarget.Name = "btnBrowseTargetImg";
            parentGroup.Controls.AddRange(new Control[] { lblTargetImage, txtTargetImage, btnBrowseTarget });
            yPos += 50;

            // 第四行：启用状态
            var chkIsEnabled = CreateCheckBox("启用此规则", 20, yPos);
            chkIsEnabled.Name = "chkImageRuleEnabled";
            chkIsEnabled.Checked = true;
            parentGroup.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 第五行：操作按钮
            var btnNewRule = CreateButton("新建规则", 20, yPos, 110);
            btnNewRule.Name = "btnNewImageRuleEdit";

            var btnSaveRule = CreateButton("保存规则", 140, yPos, 110);
            btnSaveRule.Name = "btnSaveImageRule";

            var btnClearRule = CreateButton("清空", 260, yPos, 90);
            btnClearRule.Name = "btnClearImageRule";

            parentGroup.Controls.AddRange(new Control[] { btnNewRule, btnSaveRule, btnClearRule });
        }

        /// <summary>
        /// 创建文本框替换编辑控件
        /// </summary>
        private void CreateTextBoxReplacementEditControls(TabPage tabPage)
        {
            // 上方：规则列表
            var grpRulesList = CreateGroupBox("文本框替换规则列表", 25, 25, 1090, 280);

            var listViewRules = new ListView
            {
                Name = "listViewTextBoxReplacementRules",
                Location = new Point(20, 45),
                Size = new Size(1050, 160),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            listViewRules.Columns.Add("启用", 60);
            listViewRules.Columns.Add("规则名称", 180);
            listViewRules.Columns.Add("源文本", 350);
            listViewRules.Columns.Add("目标文本", 350);
            listViewRules.Columns.Add("匹配方式", 110);

            var btnNewRule = CreateButton("新建", 20, 215, 90);
            btnNewRule.Name = "btnNewTextBoxRule";

            var btnEditRule = CreateButton("编辑", 120, 215, 90);
            btnEditRule.Name = "btnEditTextBoxRule";

            var btnDeleteRule = CreateButton("删除", 220, 215, 90);
            btnDeleteRule.Name = "btnDeleteTextBoxRule";

            grpRulesList.Controls.AddRange(new Control[] {
                listViewRules, btnNewRule, btnEditRule, btnDeleteRule
            });

            tabPage.Controls.Add(grpRulesList);

            // 下方：规则编辑区域
            var grpRuleEdit = CreateGroupBox("文本框替换规则编辑", 25, 325, 1090, 450);

            CreateTextBoxRuleEditControls(grpRuleEdit);

            tabPage.Controls.Add(grpRuleEdit);
        }

        /// <summary>
        /// 创建文本框规则编辑控件
        /// </summary>
        private void CreateTextBoxRuleEditControls(GroupBox parentGroup)
        {
            int yPos = 45;

            // 第一行：规则名称
            var lblRuleName = CreateLabel("规则名称:", 20, yPos);
            var txtRuleName = CreateTextBox(170, yPos, 400, true);
            txtRuleName.Name = "txtTextBoxRuleName";
            parentGroup.Controls.AddRange(new Control[] { lblRuleName, txtRuleName });
            yPos += 50;

            // 第二行：源文本内容
            var lblSourceText = CreateLabel("源文本内容:", 20, yPos);
            var txtSourceText = CreateMultilineTextBox(170, yPos, 850, 60); // 使用多行文本框，左对齐
            txtSourceText.Name = "txtSourceTextContent";
            parentGroup.Controls.AddRange(new Control[] { lblSourceText, txtSourceText });
            yPos += 80;

            // 第三行：目标文本内容
            var lblTargetText = CreateLabel("目标文本内容:", 20, yPos);
            var txtTargetText = CreateMultilineTextBox(170, yPos, 850, 60); // 使用多行文本框，左对齐
            txtTargetText.Name = "txtTargetTextContent";
            parentGroup.Controls.AddRange(new Control[] { lblTargetText, txtTargetText });
            yPos += 80;

            // 第四行：匹配选项
            var chkMatchByContent = CreateCheckBox("按内容匹配", 20, yPos);
            chkMatchByContent.Name = "chkMatchByContent";
            chkMatchByContent.Checked = true;

            var chkMatchByPosition = CreateCheckBox("按位置匹配", 200, yPos);
            chkMatchByPosition.Name = "chkMatchByPosition";

            var lblPosition = CreateLabel("位置(X,Y):", 380, yPos);
            var numMatchX = new NumericUpDown
            {
                Name = "numMatchX",
                Location = new Point(480, yPos),
                Size = new Size(80, 35),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                Minimum = 0,
                Maximum = 9999
            };
            var numMatchY = new NumericUpDown
            {
                Name = "numMatchY",
                Location = new Point(570, yPos),
                Size = new Size(80, 35),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular),
                Minimum = 0,
                Maximum = 9999
            };
            parentGroup.Controls.AddRange(new Control[] { chkMatchByContent, chkMatchByPosition, lblPosition, numMatchX, numMatchY });
            yPos += 50;

            // 第五行：启用状态
            var chkIsEnabled = CreateCheckBox("启用此规则", 20, yPos);
            chkIsEnabled.Name = "chkTextBoxRuleEnabled";
            chkIsEnabled.Checked = true;
            parentGroup.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 第六行：操作按钮
            var btnNewRule = CreateButton("新建规则", 20, yPos, 110);
            btnNewRule.Name = "btnNewTextBoxRuleEdit";

            var btnSaveRule = CreateButton("保存规则", 140, yPos, 110);
            btnSaveRule.Name = "btnSaveTextBoxRule";

            var btnClearRule = CreateButton("清空", 260, yPos, 90);
            btnClearRule.Name = "btnClearTextBoxRule";

            parentGroup.Controls.AddRange(new Control[] { btnNewRule, btnSaveRule, btnClearRule });
        }

        /// <summary>
        /// 创建形状样式替换编辑控件
        /// </summary>
        private void CreateShapeStyleReplacementEditControls(TabPage tabPage)
        {
            // 上方：规则列表
            var grpRulesList = CreateGroupBox("形状样式替换规则列表", 25, 25, 1090, 280);

            var listViewRules = new ListView
            {
                Name = "listViewShapeStyleReplacementRules",
                Location = new Point(20, 45),
                Size = new Size(1050, 160),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            listViewRules.Columns.Add("启用", 60);
            listViewRules.Columns.Add("规则名称", 180);
            listViewRules.Columns.Add("源形状类型", 250);
            listViewRules.Columns.Add("目标形状类型", 250);
            listViewRules.Columns.Add("替换内容", 310);

            var btnNewRule = CreateButton("新建", 20, 215, 90);
            btnNewRule.Name = "btnNewShapeStyleRule";

            var btnEditRule = CreateButton("编辑", 120, 215, 90);
            btnEditRule.Name = "btnEditShapeStyleRule";

            var btnDeleteRule = CreateButton("删除", 220, 215, 90);
            btnDeleteRule.Name = "btnDeleteShapeStyleRule";

            grpRulesList.Controls.AddRange(new Control[] {
                listViewRules, btnNewRule, btnEditRule, btnDeleteRule
            });

            tabPage.Controls.Add(grpRulesList);

            // 下方：规则编辑区域
            var grpRuleEdit = CreateGroupBox("形状样式替换规则编辑", 25, 325, 1090, 450);

            CreateShapeStyleRuleEditControls(grpRuleEdit);

            tabPage.Controls.Add(grpRuleEdit);
        }

        /// <summary>
        /// 创建形状样式规则编辑控件
        /// </summary>
        private void CreateShapeStyleRuleEditControls(GroupBox parentGroup)
        {
            int yPos = 45;

            // 第一行：规则名称
            var lblRuleName = CreateLabel("规则名称:", 20, yPos);
            var txtRuleName = CreateTextBox(170, yPos, 400, true);
            txtRuleName.Name = "txtShapeStyleRuleName";
            parentGroup.Controls.AddRange(new Control[] { lblRuleName, txtRuleName });
            yPos += 50;

            // 第二行：源形状类型和目标形状类型
            var lblSourceShapeType = CreateLabel("源形状类型:", 20, yPos);
            var cmbSourceShapeType = CreateComboBox(170, yPos, 250);
            cmbSourceShapeType.Name = "cmbSourceShapeType";
            cmbSourceShapeType.Items.AddRange(new[] { "矩形", "圆形", "椭圆", "三角形", "菱形", "五角星", "箭头", "线条", "自由形状" });

            var lblTargetShapeType = CreateLabel("目标形状类型:", 450, yPos);
            var cmbTargetShapeType = CreateComboBox(600, yPos, 250);
            cmbTargetShapeType.Name = "cmbTargetShapeType";
            cmbTargetShapeType.Items.AddRange(new[] { "矩形", "圆形", "椭圆", "三角形", "菱形", "五角星", "箭头", "线条", "自由形状" });

            parentGroup.Controls.AddRange(new Control[] { lblSourceShapeType, cmbSourceShapeType, lblTargetShapeType, cmbTargetShapeType });
            yPos += 50;

            // 第三行：颜色替换选项
            var chkReplaceFillColor = CreateCheckBox("替换填充颜色", 20, yPos);
            chkReplaceFillColor.Name = "chkReplaceFillColor";

            var chkReplaceBorderColor = CreateCheckBox("替换边框颜色", 300, yPos);
            chkReplaceBorderColor.Name = "chkReplaceBorderColor";

            parentGroup.Controls.AddRange(new Control[] { chkReplaceFillColor, chkReplaceBorderColor });
            yPos += 50;

            // 第四行：启用状态
            var chkIsEnabled = CreateCheckBox("启用此规则", 20, yPos);
            chkIsEnabled.Name = "chkShapeStyleRuleEnabled";
            chkIsEnabled.Checked = true;
            parentGroup.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 第五行：操作按钮
            var btnNewRule = CreateButton("新建规则", 20, yPos, 110);
            btnNewRule.Name = "btnNewShapeStyleRuleEdit";

            var btnSaveRule = CreateButton("保存规则", 140, yPos, 110);
            btnSaveRule.Name = "btnSaveShapeStyleRule";

            var btnClearRule = CreateButton("清空", 260, yPos, 90);
            btnClearRule.Name = "btnClearShapeStyleRule";

            parentGroup.Controls.AddRange(new Control[] { btnNewRule, btnSaveRule, btnClearRule });
        }

        /// <summary>
        /// 创建字体替换编辑区域
        /// </summary>
        private void CreateFontReplacementEditSection(Panel parentPanel, ref int yPos)
        {
            // 字体类型选择标签页
            var tabControlFontTypes = new TabControl
            {
                Location = new Point(25, yPos),
                Size = new Size(1140, 700),
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            // 字体名称替换标签页
            var tabPageFontName = new TabPage("字体名称替换");
            CreateFontNameReplacementEditControls(tabPageFontName);
            tabControlFontTypes.TabPages.Add(tabPageFontName);

            // 字体样式替换标签页
            var tabPageFontStyle = new TabPage("字体样式替换");
            CreateFontStyleReplacementEditControls(tabPageFontStyle);
            tabControlFontTypes.TabPages.Add(tabPageFontStyle);

            // 字体嵌入设置标签页
            var tabPageFontEmbedding = new TabPage("字体嵌入设置");
            CreateFontEmbeddingEditControls(tabPageFontEmbedding);
            tabControlFontTypes.TabPages.Add(tabPageFontEmbedding);

            parentPanel.Controls.Add(tabControlFontTypes);
            yPos += 720;

            // 设置滚动区域的最小尺寸
            parentPanel.AutoScrollMinSize = new Size(1140, yPos + 50);
        }

        /// <summary>
        /// 创建字体名称替换编辑控件
        /// </summary>
        private void CreateFontNameReplacementEditControls(TabPage tabPage)
        {
            // 上方：规则列表
            var grpRulesList = CreateGroupBox("字体名称替换规则列表", 25, 25, 1090, 280);

            var listViewRules = new ListView
            {
                Name = "listViewFontNameReplacementRules",
                Location = new Point(20, 45),
                Size = new Size(1050, 160),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            listViewRules.Columns.Add("启用", 60);
            listViewRules.Columns.Add("规则名称", 180);
            listViewRules.Columns.Add("源字体", 350);
            listViewRules.Columns.Add("目标字体", 350);
            listViewRules.Columns.Add("匹配选项", 110);

            var btnNewRule = CreateButton("新建", 20, 215, 90);
            btnNewRule.Name = "btnNewFontNameRule";

            var btnEditRule = CreateButton("编辑", 120, 215, 90);
            btnEditRule.Name = "btnEditFontNameRule";

            var btnDeleteRule = CreateButton("删除", 220, 215, 90);
            btnDeleteRule.Name = "btnDeleteFontNameRule";

            grpRulesList.Controls.AddRange(new Control[] {
                listViewRules, btnNewRule, btnEditRule, btnDeleteRule
            });

            tabPage.Controls.Add(grpRulesList);

            // 下方：规则编辑区域
            var grpRuleEdit = CreateGroupBox("字体名称替换规则编辑", 25, 325, 1090, 450);

            CreateFontNameRuleEditControls(grpRuleEdit);

            tabPage.Controls.Add(grpRuleEdit);
        }

        /// <summary>
        /// 创建字体名称规则编辑控件
        /// </summary>
        private void CreateFontNameRuleEditControls(GroupBox parentGroup)
        {
            int yPos = 45;

            // 第一行：规则名称
            var lblRuleName = CreateLabel("规则名称:", 20, yPos);
            var txtRuleName = CreateTextBox(170, yPos, 400, true);
            txtRuleName.Name = "txtFontNameRuleName";
            parentGroup.Controls.AddRange(new Control[] { lblRuleName, txtRuleName });
            yPos += 50;

            // 第二行：源字体名称和目标字体名称
            var lblSourceFont = CreateLabel("源字体名称:", 20, yPos);
            var cmbSourceFont = CreateComboBox(170, yPos, 300);
            cmbSourceFont.Name = "cmbSourceFont";
            cmbSourceFont.DropDownStyle = ComboBoxStyle.DropDown;
            // 添加常用字体
            cmbSourceFont.Items.AddRange(new[] { "宋体", "黑体", "楷体", "仿宋", "微软雅黑", "Arial", "Times New Roman", "Calibri" });

            var lblTargetFont = CreateLabel("目标字体名称:", 500, yPos);
            var cmbTargetFont = CreateComboBox(650, yPos, 300);
            cmbTargetFont.Name = "cmbTargetFont";
            cmbTargetFont.DropDownStyle = ComboBoxStyle.DropDown;
            // 添加常用字体
            cmbTargetFont.Items.AddRange(new[] { "宋体", "黑体", "楷体", "仿宋", "微软雅黑", "Arial", "Times New Roman", "Calibri" });

            parentGroup.Controls.AddRange(new Control[] { lblSourceFont, cmbSourceFont, lblTargetFont, cmbTargetFont });
            yPos += 50;

            // 第三行：匹配选项
            var chkExactMatch = CreateCheckBox("精确匹配", 20, yPos);
            chkExactMatch.Name = "chkExactMatch";
            chkExactMatch.Checked = true;

            var chkIncludeSubFonts = CreateCheckBox("包含子字体", 220, yPos);
            chkIncludeSubFonts.Name = "chkIncludeSubFonts";

            parentGroup.Controls.AddRange(new Control[] { chkExactMatch, chkIncludeSubFonts });
            yPos += 50;

            // 第四行：启用状态
            var chkIsEnabled = CreateCheckBox("启用此规则", 20, yPos);
            chkIsEnabled.Name = "chkFontNameRuleEnabled";
            chkIsEnabled.Checked = true;
            parentGroup.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 第五行：操作按钮
            var btnNewRule = CreateButton("新建规则", 20, yPos, 110);
            btnNewRule.Name = "btnNewFontNameRuleEdit";

            var btnSaveRule = CreateButton("保存规则", 140, yPos, 110);
            btnSaveRule.Name = "btnSaveFontNameRule";

            var btnClearRule = CreateButton("清空", 260, yPos, 90);
            btnClearRule.Name = "btnClearFontNameRule";

            parentGroup.Controls.AddRange(new Control[] { btnNewRule, btnSaveRule, btnClearRule });
        }

        /// <summary>
        /// 创建字体样式替换编辑控件
        /// </summary>
        private void CreateFontStyleReplacementEditControls(TabPage tabPage)
        {
            // 上方：规则列表
            var grpRulesList = CreateGroupBox("字体样式替换规则列表", 25, 25, 1090, 280);

            var listViewRules = new ListView
            {
                Name = "listViewFontStyleReplacementRules",
                Location = new Point(20, 45),
                Size = new Size(1050, 160),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            listViewRules.Columns.Add("启用", 60);
            listViewRules.Columns.Add("规则名称", 180);
            listViewRules.Columns.Add("源字体", 300);
            listViewRules.Columns.Add("目标字体", 300);
            listViewRules.Columns.Add("替换内容", 210);

            var btnNewRule = CreateButton("新建", 20, 215, 90);
            btnNewRule.Name = "btnNewFontStyleRule";

            var btnEditRule = CreateButton("编辑", 120, 215, 90);
            btnEditRule.Name = "btnEditFontStyleRule";

            var btnDeleteRule = CreateButton("删除", 220, 215, 90);
            btnDeleteRule.Name = "btnDeleteFontStyleRule";

            grpRulesList.Controls.AddRange(new Control[] {
                listViewRules, btnNewRule, btnEditRule, btnDeleteRule
            });

            tabPage.Controls.Add(grpRulesList);

            // 下方：规则编辑区域
            var grpRuleEdit = CreateGroupBox("字体样式替换规则编辑", 25, 325, 1090, 450);

            CreateFontStyleRuleEditControls(grpRuleEdit);

            tabPage.Controls.Add(grpRuleEdit);
        }

        /// <summary>
        /// 创建字体样式规则编辑控件
        /// </summary>
        private void CreateFontStyleRuleEditControls(GroupBox parentGroup)
        {
            int yPos = 45;

            // 第一行：规则名称
            var lblRuleName = CreateLabel("规则名称:", 20, yPos);
            var txtRuleName = CreateTextBox(170, yPos, 400, true);
            txtRuleName.Name = "txtFontStyleRuleName";
            parentGroup.Controls.AddRange(new Control[] { lblRuleName, txtRuleName });
            yPos += 50;

            // 第二行：源字体和目标字体
            var lblSourceFont = CreateLabel("源字体:", 20, yPos);
            var cmbSourceFont = CreateComboBox(170, yPos, 300);
            cmbSourceFont.Name = "cmbSourceFontStyle";
            cmbSourceFont.DropDownStyle = ComboBoxStyle.DropDown;

            var lblTargetFont = CreateLabel("目标字体:", 500, yPos);
            var cmbTargetFont = CreateComboBox(650, yPos, 300);
            cmbTargetFont.Name = "cmbTargetFontStyle";
            cmbTargetFont.DropDownStyle = ComboBoxStyle.DropDown;

            parentGroup.Controls.AddRange(new Control[] { lblSourceFont, cmbSourceFont, lblTargetFont, cmbTargetFont });
            yPos += 50;

            // 第三行：替换内容
            var lblReplaceContent = CreateLabel("替换内容:", 20, yPos);
            var txtReplaceContent = CreateTextBox(170, yPos, 780, true);
            txtReplaceContent.Name = "txtReplaceContent";
            txtReplaceContent.Text = "字体名称、字体大小、字体颜色、粗体、斜体、下划线";
            parentGroup.Controls.AddRange(new Control[] { lblReplaceContent, txtReplaceContent });
            yPos += 50;

            // 第四行：启用状态
            var chkIsEnabled = CreateCheckBox("启用此规则", 20, yPos);
            chkIsEnabled.Name = "chkFontStyleRuleEnabled";
            chkIsEnabled.Checked = true;
            parentGroup.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 第五行：操作按钮
            var btnNewRule = CreateButton("新建规则", 20, yPos, 110);
            btnNewRule.Name = "btnNewFontStyleRuleEdit";

            var btnSaveRule = CreateButton("保存规则", 140, yPos, 110);
            btnSaveRule.Name = "btnSaveFontStyleRule";

            var btnClearRule = CreateButton("清空", 260, yPos, 90);
            btnClearRule.Name = "btnClearFontStyleRule";

            parentGroup.Controls.AddRange(new Control[] { btnNewRule, btnSaveRule, btnClearRule });
        }

        /// <summary>
        /// 创建字体嵌入编辑控件
        /// </summary>
        private void CreateFontEmbeddingEditControls(TabPage tabPage)
        {
            var grpEmbedding = CreateGroupBox("字体嵌入设置", 25, 25, 1090, 600);

            int yPos = 45;

            // 嵌入选项
            var chkEmbedAllFonts = CreateCheckBox("嵌入所有字体", 30, yPos);
            chkEmbedAllFonts.Name = "chkEmbedAllFonts";
            grpEmbedding.Controls.Add(chkEmbedAllFonts);
            yPos += 60;

            var chkEmbedUsedCharactersOnly = CreateCheckBox("仅嵌入使用的字符", 30, yPos);
            chkEmbedUsedCharactersOnly.Name = "chkEmbedUsedCharactersOnly";
            chkEmbedUsedCharactersOnly.Checked = true;
            grpEmbedding.Controls.Add(chkEmbedUsedCharactersOnly);
            yPos += 60;

            // 字体列表
            var lblFontsToEmbed = CreateLabel("需要嵌入的字体:", 30, yPos);
            grpEmbedding.Controls.Add(lblFontsToEmbed);
            yPos += 50;

            var listBoxFontsToEmbed = new ListBox
            {
                Name = "listBoxFontsToEmbed",
                Location = new Point(30, yPos),
                Size = new Size(750, 250),
                SelectionMode = SelectionMode.MultiExtended,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };
            grpEmbedding.Controls.Add(listBoxFontsToEmbed);

            var btnAddFont = CreateButton("添加字体", 800, yPos, 120);
            btnAddFont.Name = "btnAddFontToEmbed";

            var btnRemoveFont = CreateButton("移除字体", 800, yPos + 60, 120);
            btnRemoveFont.Name = "btnRemoveFontToEmbed";

            var btnClearFonts = CreateButton("清空列表", 800, yPos + 120, 120);
            btnClearFonts.Name = "btnClearFontsToEmbed";

            grpEmbedding.Controls.AddRange(new Control[] { btnAddFont, btnRemoveFont, btnClearFonts });

            tabPage.Controls.Add(grpEmbedding);
        }

        /// <summary>
        /// 创建颜色替换编辑区域
        /// </summary>
        private void CreateColorReplacementEditSection(Panel parentPanel, ref int yPos)
        {
            // 上方：规则列表
            var grpRulesList = CreateGroupBox("颜色替换规则列表", 25, yPos, 1140, 400);

            var listViewRules = new ListView
            {
                Name = "listViewCustomColorReplacementRules",
                Location = new Point(20, 45),
                Size = new Size(1100, 280),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Font = new Font("Microsoft YaHei UI", 10F, FontStyle.Regular)
            };

            listViewRules.Columns.Add("启用", 60);
            listViewRules.Columns.Add("规则名称", 180);
            listViewRules.Columns.Add("源颜色", 180);
            listViewRules.Columns.Add("目标颜色", 180);
            listViewRules.Columns.Add("应用范围", 400);

            var btnNewRule = CreateButton("新建", 20, 335, 90);
            btnNewRule.Name = "btnNewColorRule";

            var btnEditRule = CreateButton("编辑", 120, 335, 90);
            btnEditRule.Name = "btnEditColorRule";

            var btnDeleteRule = CreateButton("删除", 220, 335, 90);
            btnDeleteRule.Name = "btnDeleteColorRule";

            grpRulesList.Controls.AddRange(new Control[] {
                listViewRules, btnNewRule, btnEditRule, btnDeleteRule
            });

            parentPanel.Controls.Add(grpRulesList);
            yPos += 420;

            // 下方：规则编辑区域
            var grpRuleEdit = CreateGroupBox("颜色替换规则编辑", 25, yPos, 1140, 500);

            CreateColorRuleEditControls(grpRuleEdit);

            parentPanel.Controls.Add(grpRuleEdit);
            yPos += 400;

            // 设置滚动区域的最小尺寸
            parentPanel.AutoScrollMinSize = new Size(1140, yPos + 50);
        }

        /// <summary>
        /// 创建颜色规则编辑控件
        /// </summary>
        private void CreateColorRuleEditControls(GroupBox parentGroup)
        {
            int yPos = 45;

            // 第一行：规则名称
            var lblRuleName = CreateLabel("规则名称:", 20, yPos);
            var txtRuleName = CreateTextBox(170, yPos, 400, true);
            txtRuleName.Name = "txtColorRuleName";
            parentGroup.Controls.AddRange(new Control[] { lblRuleName, txtRuleName });
            yPos += 50;

            // 第二行：源颜色和目标颜色
            var lblSourceColor = CreateLabel("源颜色:", 20, yPos);
            var btnSourceColor = CreateButton("选择源颜色", 170, yPos, 140);
            btnSourceColor.Name = "btnSourceColor";
            btnSourceColor.BackColor = Color.Black;
            btnSourceColor.ForeColor = Color.White;

            var lblTargetColor = CreateLabel("目标颜色:", 340, yPos);
            var btnTargetColor = CreateButton("选择目标颜色", 490, yPos, 140);
            btnTargetColor.Name = "btnTargetColor";
            btnTargetColor.BackColor = Color.White;
            btnTargetColor.ForeColor = Color.Black;

            parentGroup.Controls.AddRange(new Control[] { lblSourceColor, btnSourceColor, lblTargetColor, btnTargetColor });
            yPos += 50;

            // 第三行：应用范围
            var lblApplyRange = CreateLabel("应用范围:", 20, yPos);

            var chkApplyToTextColor = CreateCheckBox("文本颜色", 170, yPos);
            chkApplyToTextColor.Name = "chkApplyToTextColor";
            chkApplyToTextColor.Checked = true;

            var chkApplyToFillColor = CreateCheckBox("填充颜色", 300, yPos);
            chkApplyToFillColor.Name = "chkApplyToFillColor";
            chkApplyToFillColor.Checked = true;

            var chkApplyToBorderColor = CreateCheckBox("边框颜色", 430, yPos);
            chkApplyToBorderColor.Name = "chkApplyToBorderColor";

            var chkApplyToBackgroundColor = CreateCheckBox("背景颜色", 560, yPos);
            chkApplyToBackgroundColor.Name = "chkApplyToBackgroundColor";

            parentGroup.Controls.AddRange(new Control[] {
                lblApplyRange, chkApplyToTextColor, chkApplyToFillColor, chkApplyToBorderColor, chkApplyToBackgroundColor
            });
            yPos += 50;

            // 第四行：启用状态
            var chkIsEnabled = CreateCheckBox("启用此规则", 20, yPos);
            chkIsEnabled.Name = "chkColorRuleEnabled";
            chkIsEnabled.Checked = true;
            parentGroup.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 第五行：操作按钮
            var btnNewRule = CreateButton("新建规则", 20, yPos, 110);
            btnNewRule.Name = "btnNewColorRuleEdit";

            var btnSaveRule = CreateButton("保存规则", 140, yPos, 110);
            btnSaveRule.Name = "btnSaveColorRule";

            var btnClearRule = CreateButton("清空", 260, yPos, 90);
            btnClearRule.Name = "btnClearColorRule";

            parentGroup.Controls.AddRange(new Control[] { btnNewRule, btnSaveRule, btnClearRule });
        }

        /// <summary>
        /// 加载文本规则到编辑器
        /// </summary>
        private void LoadTextRuleToEditor(ListViewItem item)
        {
            try
            {
                var panel = _tabPanels["TextReplacement"];

                // 获取编辑控件
                var txtRuleName = FindControlByName(panel, "txtTextRuleName") is TextBox textRuleName ? textRuleName : null;
                var txtFindText = FindControlByName(panel, "txtFindText") is TextBox findText ? findText : null;
                var txtReplaceText = FindControlByName(panel, "txtReplaceText") is TextBox replaceText ? replaceText : null;
                var chkUseRegex = FindControlByName(panel, "chkUseRegex") is CheckBox useRegex ? useRegex : null;
                var chkCaseSensitive = FindControlByName(panel, "chkCaseSensitive") is CheckBox caseSensitive ? caseSensitive : null;
                var chkWholeWord = FindControlByName(panel, "chkWholeWord") is CheckBox wholeWord ? wholeWord : null;
                var cmbRangeType = FindControlByName(panel, "cmbRangeType") is ComboBox rangeType ? rangeType : null;
                var chkIsEnabled = FindControlByName(panel, "chkTextRuleEnabled") is CheckBox isEnabled ? isEnabled : null;

                // 加载数据到控件
                if (txtRuleName != null) txtRuleName.Text = item.SubItems[1].Text;
                if (txtFindText != null) txtFindText.Text = item.SubItems[2].Text;
                if (txtReplaceText != null) txtReplaceText.Text = item.SubItems[3].Text;
                if (chkUseRegex != null) chkUseRegex.Checked = item.SubItems[4].Text == "是";
                if (chkCaseSensitive != null) chkCaseSensitive.Checked = item.SubItems[5].Text == "是";
                if (chkWholeWord != null) chkWholeWord.Checked = item.SubItems[6].Text == "是";
                if (chkIsEnabled != null) chkIsEnabled.Checked = item.Checked;

                // 设置应用范围
                if (cmbRangeType != null && item.SubItems.Count > 7)
                {
                    var rangeText = item.SubItems[7].Text;
                    for (int i = 0; i < cmbRangeType.Items.Count; i++)
                    {
                        if (cmbRangeType.Items[i].ToString() == rangeText)
                        {
                            cmbRangeType.SelectedIndex = i;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载规则失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 根据名称查找控件 - 递归搜索控件树
        /// </summary>
        private static Control? FindControlByName(Control parent, string name)
        {
            if (parent.Name == name)
                return parent;

            foreach (Control child in parent.Controls)
            {
                var found = FindControlByName(child, name);
                if (found != null)
                    return found;
            }

            return null;
        }













    }
}
